# Hướng Dẫn Hệ Thống License Offline

## Tổng Quan

Hệ thống license offline này được thiết kế để bảo vệ ứng dụng IntechApplication với các tính năng:

- ✅ Hoạt động hoàn toàn offline
- ✅ License có thời hạn sử dụng
- ✅ Ký số RSA để đảm bảo tính toàn vẹn
- ✅ Ràng buộc user để tránh chia sẻ license
- ✅ Các biện pháp chống bypass thời gian hệ thống
- ✅ Kiểm tra tính toàn vẹn ứng dụng

## Cấu Trúc Hệ Thống

### 1. Main Application (IntechApplication)
- `License/LicenseManager.cs` - Class chính quản lý license
- `License/LicenseInfo.cs` - Thông tin license
- `License/LicenseFile.cs` - Cấu trúc file license
- `License/LicenseValidationResult.cs` - <PERSON><PERSON><PERSON> quả kiểm tra license
- `License/RSAHelper.cs` - Utility cho RSA encryption/signing
- `License/MachineIdentifier.cs` - Tạo machine ID duy nhất
- `License/AntiBypassProtection.cs` - <PERSON><PERSON><PERSON> biện pháp chống bypass

### 2. License Generator (LicenseGenerator)
- Console application để admin tạo license files
- Tạo cặp RSA key
- Ký và tạo license files
- Xem thông tin license

## Quy Trình Sử Dụng

### Bước 1: Tạo RSA Key Pair (Chỉ làm 1 lần)

1. Chạy LicenseGenerator console app
2. Chọn option "1. Tạo cặp RSA Key mới"
3. Nhập kích thước key (khuyến nghị 2048)
4. Copy PUBLIC KEY vào `LicenseManager.cs` (thay thế PUBLIC_KEY constant)
5. Lưu PRIVATE KEY vào file `private_key.txt` trong thư mục LicenseGenerator
6. **QUAN TRỌNG**: Bảo mật private key cẩn thận!

### Bước 2: Tạo License File

1. Chạy LicenseGenerator console app
2. Chọn option "2. Tạo License file"
3. Nhập thông tin:
   - Tên user
   - Tên công ty
   - Số ngày có hiệu lực
   - Phiên bản ứng dụng
   - Machine ID (để trống nếu không ràng buộc máy)
   - Features
4. File license sẽ được tạo với tên `license_{user}_{date}.license`

### Bước 3: Triển Khai License

1. Copy file `.license` vào thư mục gốc của ứng dụng
2. Đổi tên thành `app.license`
3. Chạy ứng dụng để kiểm tra

## Cấu Trúc License File

```json
{
  "licenseData": "{\"user\":\"John\",\"company\":\"ABC Corp\",\"issueDate\":\"2024-01-01T00:00:00\",\"expiryDate\":\"2024-12-31T23:59:59\",\"features\":[\"All\"],\"version\":\"1.0\",\"machineId\":null,\"licenseId\":\"uuid\"}",
  "signature": "base64_signature",
  "algorithm": "RSA-SHA256",
  "version": "1.0"
}
```

## Các Tính Năng Bảo Vệ

### 1. User Binding
- Lần đầu chạy: Lưu user từ license vào registry
- Lần sau: Kiểm tra user trong license phải khớp với user đã đăng ký
- Tránh việc chia sẻ license giữa các user khác nhau

### 2. Anti-Time Bypass
- Lưu thời gian cài đặt vào registry
- Kiểm tra thời gian hệ thống không được quay lại quá nhiều
- Kiểm tra pattern thời gian chạy ứng dụng

### 3. Anti-Debug Protection
- Phát hiện debugger đang attach
- Kiểm tra các process đáng ngờ (OllyDbg, x64dbg, IDA, etc.)
- Kiểm tra tính toàn vẹn file ứng dụng

### 4. Machine Binding (Tùy chọn)
- Tạo machine ID duy nhất dựa trên hardware
- Ràng buộc license với máy cụ thể

## Thông Báo Lỗi

### License Errors
- "Không tìm thấy file license" - Chưa có file app.license
- "License không hợp lệ hoặc đã bị thay đổi" - Chữ ký không đúng
- "License đã hết hạn" - Đã quá thời hạn sử dụng
- "License không được phép sử dụng trên máy này" - Machine ID không khớp
- "License này được cấp cho user khác" - User binding không khớp

### Anti-Bypass Errors
- "Phát hiện thời gian hệ thống bất thường" - Thời gian bị thay đổi
- "Phát hiện ứng dụng đã bị thay đổi" - File checksum không khớp
- "Phát hiện môi trường chạy không an toàn" - Có debugger hoặc tools hack

## Bảo Mật

### Quan Trọng
1. **Private Key**: Chỉ admin được truy cập, không được chia sẻ
2. **Public Key**: Nhúng cứng trong code, không thay đổi
3. **Registry**: Sử dụng để lưu thông tin bảo vệ
4. **File Integrity**: Kiểm tra checksum để phát hiện thay đổi

### Khuyến Nghị
- Sử dụng key size 2048 bit trở lên
- Backup private key an toàn
- Không commit private key vào source control
- Thường xuyên kiểm tra license files

## Troubleshooting

### Lỗi Thường Gặp

1. **"Không thể ký dữ liệu"**
   - Kiểm tra private key có đúng format không
   - Đảm bảo file private_key.txt tồn tại

2. **"License không hợp lệ"**
   - Kiểm tra public key trong code có đúng không
   - Đảm bảo license file không bị corrupt

3. **"Phát hiện thời gian bất thường"**
   - Kiểm tra thời gian hệ thống
   - Restart ứng dụng sau khi sửa thời gian

4. **Registry Access Errors**
   - Chạy ứng dụng với quyền admin
   - Kiểm tra Windows Registry permissions

## Gia Hạn License

1. Tạo license file mới với thời hạn mới
2. Gửi file cho user
3. User thay thế file `app.license` cũ
4. Restart ứng dụng

## Lưu Ý Kỹ Thuật

- Hệ thống sử dụng .NET 8.0
- Yêu cầu Windows (do sử dụng Registry và WMI)
- Dependencies: Newtonsoft.Json, System.Management
- License check mỗi 30 phút khi ứng dụng đang chạy

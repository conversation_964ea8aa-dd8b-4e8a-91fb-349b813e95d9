﻿<Window x:Class="IntechApplication.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:IntechApplication"
        x:Name="MainWin"
        mc:Ignorable="d"
        Title="{Binding WindowTitle}" Height="800" Width="1200"
        WindowStyle="None"
        ResizeMode="CanResize"
        AllowsTransparency="False"
        WindowStartupLocation="CenterScreen"
        Background="#F5F7FA">
    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <local:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
        <Style x:Key="VerticalTabItemStyle"
               TargetType="TabItem">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TabItem">
                        <Grid>
                            <Border x:Name="Bd"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}">
                                <ContentPresenter
                        ContentSource="Header"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center">
                                    <ContentPresenter.LayoutTransform>
                                        <RotateTransform Angle="-90"/>
                                    </ContentPresenter.LayoutTransform>
                                </ContentPresenter>
                            </Border>
                        </Grid>
                        <!-- Triggers mặc định của Material Design vẫn hoạt động
                 vì ta dựa trên BasedOn=MaterialDesignTabItem -->
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <!-- padding/size cho header -->
            <Setter Property="Height" Value="187" />
            <Setter Property="Width" Value="50" />
            <Setter Property="Background" Value="{DynamicResource TabUnselectedBackgroundBrush}"/>
            <Setter Property="Foreground" Value="{DynamicResource TabUnselectedForegroundBrush}"/>

            <Style.Triggers>
                <!-- Khi được chọn -->
                <Trigger Property="IsSelected" Value="True">
                    <Setter Property="Background" Value="{DynamicResource TabSelectedBackgroundBrush}"/>
                    <Setter Property="Foreground" Value="{DynamicResource TabSelectedForegroundBrush}"/>
                </Trigger>

                <!-- Hover 
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{DynamicResource TabHoverBackgroundBrush}"/>
                    <Setter Property="Foreground" Value="{DynamicResource TabHoverForegroundBrush}"/>
                </Trigger> 
                -->
                <!-- Disabled -->
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Foreground" Value="{DynamicResource MaterialDesignDisabledForeground}"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        <Style TargetType="Button" x:Key="TitleBarIconButton"
               BasedOn="{StaticResource MaterialDesignIconButton}">
            <Setter Property="Width" Value="40"/>
            <Setter Property="Height" Value="40"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="50"/>
            <!-- Title bar -->
            <RowDefinition Height="*"/>
            <!-- Content -->
        </Grid.RowDefinitions>
        <!-- Title bar -->
        <materialDesign:ColorZone Grid.Row="0" Mode="Light"
                                  MouseLeftButtonDown="TitleBar_MouseLeftButtonDown"
                                  MouseRightButtonUp="TitleBar_MouseRightButtonUp">
            <Grid x:Name="TitleBarZone">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- App icon / logo -->
                <Menu Panel.ZIndex="1">
                    <MenuItem Header="{DynamicResource SettingsIcon}" Height="50">
                        <MenuItem Header="Save" Icon="{materialDesign:PackIcon Kind=ContentSave}" />
                        <MenuItem Header="Save As.." />
                        <MenuItem Header="Exit"
                                  Icon="{materialDesign:PackIcon Kind=ExitToApp}"
                                  InputGestureText="Ctrl+E" />
                        <Separator />
                        <MenuItem Header="Excellent"
                                  IsCheckable="True"
                                  IsChecked="True" />
                        <MenuItem Header="Rubbish" IsCheckable="True" />
                        <MenuItem Header="Dig Deeper" InputGestureText="Ctrl+D">
                            <MenuItem Header="Enlightenment?" IsCheckable="True" />
                            <MenuItem Header="Disappointment" IsCheckable="True" />
                        </MenuItem>
                        <MenuItem Header="Look Deeper" InputGestureText="Ctrl+D">
                            <MenuItem Header="Plain" />
                            <MenuItem Header="Ice Cream" />
                        </MenuItem>
                    </MenuItem>
                    <MenuItem Header="{DynamicResource NofityIcon}">
                        <MenuItem Command="Cut" Header="_Cut" Icon="{materialDesign:PackIcon Kind=ContentCut}" />
                        <MenuItem Command="Copy" Header="_Copy" Icon="{materialDesign:PackIcon Kind=ContentCopy}" />
                        <MenuItem Command="Paste" Header="_Paste" Icon="{materialDesign:PackIcon Kind=ContentPaste}" />
                    </MenuItem>
                </Menu>

                <!-- Title -->
                <TextBlock Grid.Column="1"
                           Text="{Binding WindowTitle}"
                           VerticalAlignment="Center"
                           FontWeight="SemiBold"
                           TextTrimming="CharacterEllipsis"/>

                <!-- Control buttons -->
                <StackPanel x:Name="TitleButtonsPanel" Grid.Column="2" Orientation="Horizontal">
                    <!-- Minimize -->
                    <Button Style="{StaticResource TitleBarIconButton}"
                            x:Name="BtnMin"
                            ToolTip="Minimize"
                            Click="Minimize_Click" VerticalAlignment="Bottom">
                        <materialDesign:PackIcon Kind="WindowMinimize" />
                    </Button>

                    <!-- Maximize / Restore -->
                    <Button Style="{StaticResource TitleBarIconButton}"
                            x:Name="BtnMax"
                            ToolTip="Maximize/Restore"
                            Click="MaxRestore_Click">
                        <materialDesign:PackIcon x:Name="MaxIcon" Width="25" Height="25">
                            <materialDesign:PackIcon.Style>
                                <Style TargetType="materialDesign:PackIcon">
                                    <!-- mặc định khi Window ở Normal -->
                                    <Setter Property="Kind" Value="SquareRoundedOutline"/>
                                    <Style.Triggers>
                                        <!-- khi WindowState = Maximized thì đổi icon -->
                                        <DataTrigger Binding="{Binding ElementName=MainWin, Path=WindowState}" Value="Maximized">
                                            <Setter Property="Kind" Value="DockWindow"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </materialDesign:PackIcon.Style>
                        </materialDesign:PackIcon>
                    </Button>

                    <!-- Close -->
                    <Button Style="{StaticResource TitleBarIconButton}"
                            x:Name="BtnClose"
                            ToolTip="Close"
                            Click="Close_Click">
                        <materialDesign:PackIcon Kind="WindowClose" HorizontalAlignment="Center" VerticalAlignment="Center" />
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>
        <!-- Content -->
        <Grid  Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <!-- Main Content Area -->
            <Grid Grid.Row="1" Background="Transparent">
            <!-- Tab control -->
            <materialDesign:Card Background="Transparent">
                <Grid>
                    <Viewbox Grid.ColumnSpan="3"
                             Panel.ZIndex="99"
                             MaxHeight="190"
                             MaxWidth="190"
                             Margin="55,0,0,5"
                             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                             Stretch="Uniform" HorizontalAlignment="Left" VerticalAlignment="Bottom">
                        <!-- Logo -->
                        <Canvas Name="Layer_1" Canvas.Left="0" Canvas.Top="0" Width="450" Height="127">
                            <Canvas.RenderTransform>
                                <TranslateTransform X="0" Y="0"/>
                            </Canvas.RenderTransform>
                            <Canvas.Resources/>
                            <!--Unknown tag: sodipodi:namedview-->
                            <Canvas Name="g16">
                                <Path xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" Name="path4" Fill="#008c48">
                                    <Path.Data>
                                        <PathGeometry Figures="M4.8 126.73h78.83c21.32 0 38.77-17.53 38.77-38.96V9.16c0-2.68-2.18-4.87-4.85-4.87H75.03   c1.99 2.77 3.16 6.17 3.16 9.83c0 3.66-1.17 7.06-3.16 9.83h27.71v63.02c0 11.02-9.09 20.11-20.11 20.11H19.61V42.52   c0-11.02 7.56-18.58 18.58-18.58h9.13c-1.99-2.77-3.16-6.16-3.16-9.83c0-3.67 1.17-7.06 3.16-9.83h-8.6   C17.4 4.29-0.05 21.82-0.05 43.24v78.62C-0.05 124.54 2.13 126.73 4.8 126.73" FillRule="NonZero"/>
                                    </Path.Data>
                                </Path>
                                <Path xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" Name="path6" Fill="#000000">
                                    <Path.Data>
                                        <PathGeometry Figures="M83.76 103.53H61.39c-10.5 0-12.52-3.29-12.49-14.54l0.17-55.53h24.16L73.11 88.4h26.07   C98.46 96.46 91.85 102.98 83.76 103.53 M61.17-0.46c8.08 0 14.63 6.52 14.63 14.55c0 8.04-6.55 14.55-14.63 14.55   c-8.08 0-14.63-6.52-14.63-14.55C46.55 6.06 53.1-0.46 61.17-0.46z" FillRule="NonZero"/>
                                    </Path.Data>
                                </Path>
                                <Path xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" Name="path8" Fill="#008c48">
                                    <Path.Data>
                                        <PathGeometry Figures="M422.96 126.73v-25.86h20.93c1.89 0 3.28 0.39 4.15 1.15c0.87 0.76 1.3 2.06 1.3 3.87v5.75   c0 1.77-0.44 3.06-1.32 3.84c-0.87 0.78-2.25 1.18-4.13 1.18h-15.33v10.06H422.96z M441.48 105.36h-12.92v6.83h12.92   c0.92 0 1.59-0.14 1.99-0.43c0.39-0.29 0.59-0.84 0.59-1.65v-2.67c0-0.84-0.2-1.39-0.6-1.66   C443.05 105.5 442.39 105.36 441.48 105.36z M392.4 100.87v21.02h16.16v-21.02h5.71v19.73c0 1.64-0.19 2.88-0.57 3.75   c-0.37 0.87-1.01 1.48-1.89 1.83c-0.89 0.36-2.11 0.54-3.66 0.54h-15.38c-1.56 0-2.77-0.18-3.65-0.54   c-0.88-0.36-1.49-0.97-1.85-1.84c-0.36-0.88-0.54-2.12-0.54-3.74v-19.73H392.4z M354.23 121.77h18.72v-16.11h-18.72V121.77z    M348.58 106.97c0-1.65 0.18-2.92 0.55-3.78c0.37-0.87 0.97-1.47 1.82-1.8c0.85-0.34 2.07-0.51 3.66-0.51h17.97   c1.59 0 2.81 0.17 3.66 0.51c0.85 0.34 1.46 0.94 1.82 1.8c0.37 0.87 0.55 2.13 0.55 3.78v13.64c0 1.64-0.18 2.89-0.55 3.77   c-0.37 0.87-0.99 1.49-1.87 1.83c-0.87 0.35-2.08 0.52-3.61 0.52h-17.97c-1.55 0-2.77-0.17-3.64-0.52   c-0.88-0.35-1.49-0.97-1.85-1.83c-0.36-0.88-0.54-2.13-0.54-3.77V106.97z M333.58 105.36h-12.92v7.09h12.92   c0.91 0 1.57-0.14 1.97-0.43c0.4-0.28 0.6-0.84 0.6-1.64v-2.96c0-0.83-0.2-1.38-0.6-1.65C335.14 105.5 334.48 105.36 333.58 105.36   z M315.05 126.73v-25.86h20.93c1.88 0 3.26 0.39 4.13 1.16c0.88 0.77 1.32 2.06 1.32 3.85v6.1c0 1.75-0.44 3.02-1.33 3.79   c-0.89 0.77-2.26 1.16-4.12 1.16h-3.19l11.05 9.79h-8.13l-9.73-9.79h-5.34v9.79H315.05z M301.48 105.65H283.8v16.11h17.67v-5.48   h-9.23v-4.61h14.77v8.93c0 1.61-0.18 2.86-0.54 3.74c-0.36 0.87-0.97 1.49-1.85 1.84c-0.87 0.36-2.09 0.54-3.64 0.54h-16.81   c-1.55 0-2.77-0.18-3.64-0.54c-0.88-0.36-1.49-0.97-1.85-1.84c-0.36-0.88-0.54-2.12-0.54-3.74v-13.64c0-1.64 0.18-2.88 0.55-3.75   c0.37-0.87 0.99-1.48 1.87-1.82c0.87-0.35 2.08-0.52 3.62-0.52h16.81c1.56 0 2.75 0.16 3.59 0.49c0.84 0.34 1.46 0.93 1.86 1.77   c0.39 0.85 0.59 2.03 0.59 3.55v0.69l-5.55 1.04V105.65z" FillRule="NonZero"/>
                                    </Path.Data>
                                </Path>
                                <Path xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" Name="path10" Fill="#008c48">
                                    <Path.Data>
                                        <PathGeometry Figures="M119.45 126.73h142.42V113.8h-132.2C126.94 118.63 123.48 122.99 119.45 126.73" FillRule="NonZero"/>
                                    </Path.Data>
                                </Path>
                                <Path xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" Name="path12" Fill="#000000">
                                    <Path.Data>
                                        <PathGeometry Figures="M132.76 107.34h129.11v-6.47H134.86C134.3 103.08 133.59 105.24 132.76 107.34" FillRule="NonZero"/>
                                    </Path.Data>
                                </Path>
                                <Path xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" Name="path14" Fill="#000000">
                                    <Path.Data>
                                        <PathGeometry Figures="M170.81 77.06c0.2 4.75-0.98 6.62-3.52 8.89c-1.82 1.63-4.04 2.43-6.65 2.43h-10.82   c-5.41 0-8.9-3.23-10.49-9.71c-0.45-1.82-0.66-3.78-0.66-5.88l0.15-39.37h16.65l-0.12 43.65H170.81z M230.76 77.06   c0.18 4.75-1.12 6.61-3.92 8.89c-1.98 1.61-4.33 2.43-7.06 2.43h-1.72c-5.16 0-8.35-2.43-9.55-7.28c-0.39-1.6-0.59-3.5-0.6-5.66   l0.02-19.01l-15.31 25.16c-3 5.16-6.66 6.79-9.6 6.79h-8.15l-0.02-54.96h16.56l-0.04 27.48l13.93-21.82   c2.92-4.57 3.37-6.63 8.72-6.63h4.04c3.87 0 5.06 3.86 5.88 7.18c0.5 2.01 0.76 4.25 0.75 6.73l-0.14 30.71H230.76z M272.26 77.06   c0.35 8.64-3.31 11.32-10.98 11.32h-13.21c-5.58 0-9.08-2.81-10.46-8.42c-0.34-1.38-0.48-2.9-0.48-4.53l0.02-30.71h-9.3l-0.31-9.69   c9.08-1.07 15.56-9.91 16.76-30.34h9.41l0.01 28.71h16.77c-0.22 8.61-3.9 11.32-11.07 11.32h-5.91l0.1 32.33H272.26z M388.64 77.06   c0.21 4.22-0.72 5.87-2.78 8.17c-1.89 2.11-4.47 3.15-7.67 3.15h-27.11c-18.17 0-23.96-35.3-12.69-47.76   c4.33-4.79 10.25-7.2 17.77-7.2h14.26c9.6 0 15.57 5.21 17.36 11.26c1.74 5.88-0.08 14.75-7.23 16.01   c-15.61 2.75-13.77-15.82-13.78-15.88l-13.12-0.07l-0.02 21.1c0.02 5.16 0.98 11.23 7.3 11.23H388.64z M146.33 9.11   c5.84 0 10.58 4.74 10.58 10.58c0 5.84-4.74 10.58-10.58 10.58s-10.58-4.74-10.58-10.58C135.75 13.84 140.48 9.11 146.33 9.11z    M294.57 44.73v11.06h18.66V44.73H294.57z M294.77 67.11c0.31 5.39 2.34 9.95 6.51 9.95h27.87c0.23 4.17-0.78 5.8-3.04 8.17   c-1.99 2.1-4.64 3.15-7.88 3.15h-22.05c-18.03 0-19.83-12.01-20.45-24.73c-0.41-8.37 0.33-17.04 5.18-22.82   c4.17-4.95 9.98-7.42 17.42-7.42h8.22c10.32 0 18.77 5.03 21.87 15.92c2.31 8.09-1.76 17.78-11.79 17.77L294.77 67.11z    M449.32 77.06c0.18 4.75-1.04 6.63-3.69 8.89c-1.91 1.63-4.18 2.43-6.82 2.43h-3.12c-4.27 0-6.94-2.21-8.03-6.62   c-0.43-1.74-0.67-3.84-0.71-6.32l-0.17-18.84L409.9 83.44c-1.94 3.21-4.21 4.94-7.15 4.94h-9.4c0.05-32.55 0.11-51.17 0.16-83.72   c19.73 0 16.5 8.23 16.47 30.96l-0.08 25.44l13.91-21.82c2.01-3.15 3.72-6.77 7.78-6.77h5.02c3.87 0 5.07 3.85 5.89 7.16   c0.5 2.01 0.67 4.3 0.67 6.87l-0.05 30.57H449.32z" FillRule="NonZero"/>
                                    </Path.Data>
                                </Path>
                            </Canvas>
                        </Canvas>
                    </Viewbox>
                        <TabControl x:Name="MainTabs"
                                    SelectionChanged="MainTabs_SelectionChanged"
                                    TabStripPlacement="Left"
                                    FontSize="18"
                                    Style="{StaticResource MaterialDesignTabControl}"
                                    ItemContainerStyle="{StaticResource VerticalTabItemStyle}">
                        <TabItem Header="Tính toán">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="10"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <!-- Search module -->
                                <Grid Grid.Column="0" Width="250" Background="#FFF">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>
                                    <!-- Search TextBox -->
                                        <Border Grid.RowSpan="2" BorderBrush="#c1c0c1" BorderThickness="1"></Border>
                                        <Border CornerRadius="8" Background="#f6f5fa" Margin="5,10,5,5"
                                                Panel.ZIndex="1"
                                                Grid.Row="0">
                                            <TextBox x:Name="SearchTextBox"
                                                Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="SearchTextBox_TextChanged"
                                                GotFocus="SearchTextBox_GotFocus"
                                                LostFocus="SearchTextBox_LostFocus"
                                                BorderThickness="0"
                                                Foreground="#FF001BFF"
                                                Background="Transparent"
                                                Padding="10"
                                                FontSize="14"
                                                materialDesign:HintAssist.Hint="Tìm kiếm..."
                                                materialDesign:HintAssist.IsFloating="True"
                                                materialDesign:TextFieldAssist.HasClearButton="True"
                                                materialDesign:TextFieldAssist.HasLeadingIcon="True"
                                                materialDesign:TextFieldAssist.LeadingIcon="Search"
                                                />
                                        </Border>
                                    <!-- Search Results ListBox -->
                                    <ListBox x:Name="SearchResultsListBox"
                                             Grid.Row="1"
                                             Foreground="Black"
                                             ItemsSource="{Binding FilteredResults}"
                                             BorderThickness="1"
                                             SelectionChanged="SearchResultsListBox_SelectionChanged"
                                             VerticalContentAlignment="Stretch"
                                             ScrollViewer.VerticalScrollBarVisibility="Auto"
                                             ScrollViewer.HorizontalScrollBarVisibility="Auto"
                                             HorizontalContentAlignment="Stretch"
                                             MaxHeight="400" VerticalAlignment="Top">
                                        <ListBox.ItemTemplate>
                                            <DataTemplate>
                                                <Border CornerRadius="8" Background="#f6f5fa" Margin="5,10,0,5">
                                                    <TextBlock Text="{Binding Title}" Padding="10" FontWeight="Bold" FontSize="16"/>
                                                </Border>
                                            </DataTemplate>
                                        </ListBox.ItemTemplate>
                                        <ListBox.ItemContainerStyle>
                                            <Style TargetType="ListBoxItem">
                                                <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                                <Setter Property="Padding" Value="0"/>
                                                <Style.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#f0f8ff"/>
                                                    </Trigger>
                                                </Style.Triggers>
                                            </Style>
                                        </ListBox.ItemContainerStyle>
                                    </ListBox>
                                </Grid>
                                <!-- Main content area -->
                                    <ContentControl Grid.Column="2"
                                                    Content="{Binding CalculationContent}"
                                                    Foreground="#2E49E0"
                                                    Background="#F5F7FA"
                                                    FontSize="16">
                                        <ContentControl.Resources>
                                            
                                        </ContentControl.Resources>
                                    </ContentControl>
                                    <!-- Side bar - additional options -->
                                <materialDesign:DrawerHost
                                    Grid.ColumnSpan="3"
                                    Panel.ZIndex="2"
                                    x:Name="DrawerHost"
                                    BorderBrush="{DynamicResource MaterialDesignDivider}"
                                    BorderThickness="2"
                                    BottomDrawerBackground="{DynamicResource SecondaryHueLightBrush}"
                                    BottomDrawerCornerRadius="20 20 0 0">
                                    <materialDesign:DrawerHost.Style>
                                        <Style TargetType="materialDesign:DrawerHost" BasedOn="{StaticResource {x:Type materialDesign:DrawerHost}}">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsChecked, ElementName=BackgroundToggle}" Value="True">
                                                    <Setter Property="OverlayBackground" Value="{DynamicResource PrimaryHueMidBrush}" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </materialDesign:DrawerHost.Style>
                                    <materialDesign:DrawerHost.RightDrawerContent>
                                        <StackPanel Margin="16">
                                            <TextBlock
                                        Margin="4"
                                        HorizontalAlignment="Center"
                                        Text="Trợ giúp" />
                                            <Button
                                        Margin="4"
                                        HorizontalAlignment="Center"
                                        Command="{Binding Option1Command}"
                                        CommandParameter="{x:Static Dock.Right}"
                                        Content="Hướng dẫn nhập liệu"
                                        Style="{StaticResource MaterialDesignFlatButton}" />
                                            <Button
                                        Margin="4"
                                        HorizontalAlignment="Center"
                                        Command="{Binding Option2Command}"
                                        Content="Hướng dẫn sử dụng công thức"
                                        Style="{StaticResource MaterialDesignFlatButton}" />
                                        </StackPanel>
                                    </materialDesign:DrawerHost.RightDrawerContent>
                                    <Grid HorizontalAlignment="Right" VerticalAlignment="Center" >
                                        <Button
                                    Height="40"
                                    Padding="0"
                                    Background="Transparent"
                                    BorderBrush="Transparent"
                                    Command="{x:Static materialDesign:DrawerHost.OpenDrawerCommand}"
                                    CommandParameter="{x:Static Dock.Right}"
                                    Content="{materialDesign:PackIcon Kind=ChevronLeft,Size=40}">
                                        </Button>
                                    </Grid>
                                </materialDesign:DrawerHost>
                            </Grid>
                        </TabItem>
                        <TabItem Header="Chọn thiết bị">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="10"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid Grid.Column="0" Width="250" Background="#FFF">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>
                                    <!-- Search TextBox -->
                                        <Border Grid.RowSpan="2" BorderBrush="#c1c0c1" BorderThickness="1"></Border>
                                        <Border CornerRadius="8" Background="#f6f5fa" Margin="5,10,5,5"
                                                Panel.ZIndex="1"
                                                Grid.Row="0">
                                            <TextBox x:Name="SearchTextBoxTab2"
                                                Text="{Binding DeviceSearchText, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="DeviceSearchTextBox_TextChanged"
                                                GotFocus="DeviceSearchTextBox_GotFocus"
                                                LostFocus="DeviceSearchTextBox_LostFocus"
                                                BorderThickness="0"
                                                Foreground="#FF001BFF"
                                                Background="Transparent"
                                                Padding="10"
                                                FontSize="14"
                                                materialDesign:HintAssist.Hint="Tìm kiếm..."
                                                materialDesign:HintAssist.IsFloating="True"
                                                materialDesign:TextFieldAssist.HasClearButton="True"
                                                materialDesign:TextFieldAssist.HasLeadingIcon="True"
                                                materialDesign:TextFieldAssist.LeadingIcon="Search"
                                                />
                                        </Border>

                                    <!-- Search Results ListBox -->
                                    <ListBox x:Name="SearchResultsListBoxTab2"
                                             Grid.Row="1"
                                             Foreground="Black"
                                             ItemsSource="{Binding FilteredDeviceResults}"
                                             BorderThickness="1"
                                             SelectionChanged="DeviceSearchResultsListBox_SelectionChanged"
                                             VerticalContentAlignment="Stretch"
                                             ScrollViewer.VerticalScrollBarVisibility="Auto"
                                             ScrollViewer.HorizontalScrollBarVisibility="Auto"
                                             HorizontalContentAlignment="Stretch"
                                             MaxHeight="400" VerticalAlignment="Top">
                                        <ListBox.ItemTemplate>
                                            <DataTemplate>
                                                <Border CornerRadius="8" Background="#f6f5fa" Margin="5,10,0,5">
                                                    <TextBlock Text="{Binding Title}" Padding="10" FontWeight="Bold" FontSize="16"/>
                                                </Border>
                                            </DataTemplate>
                                        </ListBox.ItemTemplate>
                                        <ListBox.ItemContainerStyle>
                                            <Style TargetType="ListBoxItem">
                                                <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                                <Setter Property="Padding" Value="0"/>
                                                <Style.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#f0f8ff"/>
                                                    </Trigger>
                                                </Style.Triggers>
                                            </Style>
                                        </ListBox.ItemContainerStyle>
                                    </ListBox>
                                </Grid>
                                <!-- Main content area -->
                                <ContentControl Grid.Column="2" Content="{Binding DeviceContent}" />
                            </Grid>
                        </TabItem>
                        <TabItem Header="Tiêu chuẩn">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="10"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid Grid.Column="0" Width="250" Background="#FFF">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>
                                    <!-- Search TextBox -->
                                        <Border Grid.RowSpan="2" BorderBrush="#c1c0c1" BorderThickness="1"></Border>
                                        <Border CornerRadius="8" Background="#f6f5fa" Margin="5,10,5,5"
                                                Panel.ZIndex="1"
                                                Grid.Row="0">
                                            <TextBox x:Name="SearchTextBoxTab3"
                                                Text="{Binding StandardsSearchText, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="StandardsSearchTextBox_TextChanged"
                                                GotFocus="StandardsSearchTextBox_GotFocus"
                                                LostFocus="StandardsSearchTextBox_LostFocus"
                                                BorderThickness="0"
                                                Foreground="#FF001BFF"
                                                Background="Transparent"
                                                Padding="10"
                                                FontSize="14"
                                                materialDesign:HintAssist.Hint="Tìm kiếm..."
                                                materialDesign:HintAssist.IsFloating="True"
                                                materialDesign:TextFieldAssist.HasClearButton="True"
                                                materialDesign:TextFieldAssist.HasLeadingIcon="True"
                                                materialDesign:TextFieldAssist.LeadingIcon="Search"
                                                />
                                        </Border>

                                    <!-- Search Results ListBox -->
                                    <ListBox x:Name="SearchResultsListBoxTab3"
                                             Grid.Row="1"
                                             Foreground="Black"
                                             ItemsSource="{Binding FilteredStandardsResults}"
                                             BorderThickness="1"
                                             SelectionChanged="StandardsSearchResultsListBox_SelectionChanged"
                                             VerticalContentAlignment="Stretch"
                                             ScrollViewer.VerticalScrollBarVisibility="Auto"
                                             ScrollViewer.HorizontalScrollBarVisibility="Auto"
                                             HorizontalContentAlignment="Stretch"
                                             MaxHeight="400" VerticalAlignment="Top">
                                        <ListBox.ItemTemplate>
                                            <DataTemplate>
                                                <Border CornerRadius="8" Background="#f6f5fa" Margin="5,10,0,5">
                                                    <TextBlock Text="{Binding Title}" Padding="10" FontWeight="Bold" FontSize="16"/>
                                                </Border>
                                            </DataTemplate>
                                        </ListBox.ItemTemplate>
                                        <ListBox.ItemContainerStyle>
                                            <Style TargetType="ListBoxItem">
                                                <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                                <Setter Property="Padding" Value="0"/>
                                                <Style.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#f0f8ff"/>
                                                    </Trigger>
                                                </Style.Triggers>
                                            </Style>
                                        </ListBox.ItemContainerStyle>
                                    </ListBox>
                                </Grid>
                                <!-- Main content area -->
                                <ContentControl Grid.Column="2" Content="{Binding StandardsContent}" />
                            </Grid>
                        </TabItem>
                        <TabItem Header="Kỹ năng">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="10"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid Grid.Column="0" Width="250" Background="#FFF">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>
                                    <!-- Search TextBox -->
                                        <Border Grid.RowSpan="2" BorderBrush="#c1c0c1" BorderThickness="1"></Border>
                                        <Border CornerRadius="8" Background="#f6f5fa" Margin="5,10,5,5"
                                                Panel.ZIndex="1"
                                                Grid.Row="0">
                                            <TextBox x:Name="SearchTextBoxTab4"
                                                Text="{Binding SoftSkillsSearchText, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="SoftSkillsSearchTextBox_TextChanged"
                                                GotFocus="SoftSkillsSearchTextBox_GotFocus"
                                                LostFocus="SoftSkillsSearchTextBox_LostFocus"
                                                BorderThickness="0"
                                                Foreground="#FF001BFF"
                                                Background="Transparent"
                                                Padding="10"
                                                FontSize="14"
                                                materialDesign:HintAssist.Hint="Tìm kiếm..."
                                                materialDesign:HintAssist.IsFloating="True"
                                                materialDesign:TextFieldAssist.HasClearButton="True"
                                                materialDesign:TextFieldAssist.HasLeadingIcon="True"
                                                materialDesign:TextFieldAssist.LeadingIcon="Search"
                                                />
                                        </Border>

                                    <!-- Search Results ListBox -->
                                    <ListBox x:Name="SearchResultsListBoxTab4"
                                             Grid.Row="1"
                                             Foreground="Black"
                                             ItemsSource="{Binding FilteredSoftSkillsResults}"
                                             BorderThickness="1"
                                             SelectionChanged="SoftSkillsSearchResultsListBox_SelectionChanged"
                                             VerticalContentAlignment="Stretch"
                                             ScrollViewer.VerticalScrollBarVisibility="Auto"
                                             ScrollViewer.HorizontalScrollBarVisibility="Auto"
                                             HorizontalContentAlignment="Stretch"
                                             MaxHeight="400" VerticalAlignment="Top">
                                        <ListBox.ItemTemplate>
                                            <DataTemplate>
                                                <Border CornerRadius="8" Background="#f6f5fa" Margin="5,10,0,5">
                                                    <TextBlock Text="{Binding Title}" Padding="10" FontWeight="Bold" FontSize="16"/>
                                                </Border>
                                            </DataTemplate>
                                        </ListBox.ItemTemplate>
                                        <ListBox.ItemContainerStyle>
                                            <Style TargetType="ListBoxItem">
                                                <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                                <Setter Property="Padding" Value="0"/>
                                                <Style.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#f0f8ff"/>
                                                    </Trigger>
                                                </Style.Triggers>
                                            </Style>
                                        </ListBox.ItemContainerStyle>
                                    </ListBox>
                                </Grid>
                                <!-- Main content area -->
                                <ContentControl Grid.Column="2" Content="{Binding SoftSkillsContent}" />
                            </Grid>
                        </TabItem>
                    </TabControl>
                </Grid>
            </materialDesign:Card>
        </Grid>
        </Grid>
    </Grid>
</Window>

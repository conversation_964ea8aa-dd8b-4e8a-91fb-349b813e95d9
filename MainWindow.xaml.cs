using IntechApplication.CalculationControl;
using IntechApplication.License;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.Globalization;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using System.Windows.Threading;

namespace IntechApplication
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window, INotifyPropertyChanged
    {
        private readonly string _baseTitle = "Bộ công cụ - Intech group";
        private string? _currentTabTitle;
        private string? _currentContentTitle;
        private DispatcherTimer _licenseCheckTimer;
        public string WindowTitle
        {
            get
            {
                var title = _baseTitle;
                if (!string.IsNullOrWhiteSpace(_currentTabTitle)) title += " - " + _currentTabTitle;
                if (!string.IsNullOrWhiteSpace(_currentContentTitle)) title += " - " + _currentContentTitle;
                return title;
            }
        }
        private void SetTabTitle(string? tabTitle)
        {
            _currentTabTitle = string.IsNullOrWhiteSpace(tabTitle) ? null : tabTitle.Trim();
            OnPropertyChanged(nameof(WindowTitle));
        }
        private void SetContentTitle(string? contentTitle)
        {
            _currentContentTitle = string.IsNullOrWhiteSpace(contentTitle) ? null : contentTitle.Trim();
            OnPropertyChanged(nameof(WindowTitle));
        }
        private void MainTabs_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is TabControl tc && tc.SelectedItem is TabItem ti)
            {
                // Lấy tên tab: ưu tiên TabItem.Tag, sau đó Header string/TextBlock, cuối cùng ToString
                string? tabName = ti.Tag as string
                                  ?? (ti.Header as string)
                                  ?? (ti.Header as TextBlock)?.Text
                                  ?? ti.Header?.ToString();

                SetTabTitle(tabName);

                // Khi đổi tab, thường nên xóa hậu tố nội dung (tùy bạn):
                SetContentTitle(null);
            }
        }
        public class SearchResultItem
        {
            public string Title { get; set; } = string.Empty;          // Hiển thị trong SearchBox
            //public string Description { get; set; } = string.Empty;    // Mô tả phụ
            public Type ViewType { get; set; } = typeof(object);         // UserControl tương ứng
        }

        public class DocumentItem
        {
            public string Title { get; set; } = string.Empty;          // Tiêu đề document
            //public string Description { get; set; } = string.Empty;    // Mô tả document
            public string FilePath { get; set; } = string.Empty;       // Đường dẫn file Word
        }
        // Tab 1: Tính toán
        private string _searchText = "";
        public string SearchText
        {
            get => _searchText;
            set
            {
                if (_searchText != value)
                {
                    _searchText = value;
                    OnPropertyChanged(nameof(SearchText));
                    FilterResults();
                }
            }
        }

        private bool _isSearchResultsVisible;
        public bool IsSearchResultsVisible
        {
            get => _isSearchResultsVisible;
            set
            {
                if (_isSearchResultsVisible != value)
                {
                    _isSearchResultsVisible = value;
                    OnPropertyChanged(nameof(IsSearchResultsVisible));
                }
            }
        }

        // Tab 2: Lựa chọn thiết bị
        private string _deviceSearchText = "";
        public string DeviceSearchText
        {
            get => _deviceSearchText;
            set
            {
                if (_deviceSearchText != value)
                {
                    _deviceSearchText = value;
                    OnPropertyChanged(nameof(DeviceSearchText));
                    FilterDeviceResults();
                }
            }
        }

        private bool _isDeviceResultsVisible;
        public bool IsDeviceResultsVisible
        {
            get => _isDeviceResultsVisible;
            set
            {
                if (_isDeviceResultsVisible != value)
                {
                    _isDeviceResultsVisible = value;
                    OnPropertyChanged(nameof(IsDeviceResultsVisible));
                }
            }
        }

        // Tab 3: Tiêu chuẩn cơ sở
        private string _standardsSearchText = "";
        public string StandardsSearchText
        {
            get => _standardsSearchText;
            set
            {
                if (_standardsSearchText != value)
                {
                    _standardsSearchText = value;
                    OnPropertyChanged(nameof(StandardsSearchText));
                    FilterStandardsResults();
                }
            }
        }

        private bool _isStandardsResultsVisible;
        public bool IsStandardsResultsVisible
        {
            get => _isStandardsResultsVisible;
            set
            {
                if (_isStandardsResultsVisible != value)
                {
                    _isStandardsResultsVisible = value;
                    OnPropertyChanged(nameof(IsStandardsResultsVisible));
                }
            }
        }

        // Tab 4: Bộ kỹ năng mềm
        private string _softSkillsSearchText = "";
        public string SoftSkillsSearchText
        {
            get => _softSkillsSearchText;
            set
            {
                if (_softSkillsSearchText != value)
                {
                    _softSkillsSearchText = value;
                    OnPropertyChanged(nameof(SoftSkillsSearchText));
                    FilterSoftSkillsResults();
                }
            }
        }

        private bool _isSoftSkillsResultsVisible;
        public bool IsSoftSkillsResultsVisible
        {
            get => _isSoftSkillsResultsVisible;
            set
            {
                if (_isSoftSkillsResultsVisible != value)
                {
                    _isSoftSkillsResultsVisible = value;
                    OnPropertyChanged(nameof(IsSoftSkillsResultsVisible));
                }
            }
        }

        // Separate content properties for each tab
        private UserControl? _calculationContent;
        public UserControl? CalculationContent
        {
            get => _calculationContent;
            set
            {
                _calculationContent = value;
                OnPropertyChanged(nameof(CalculationContent));
            }
        }

        private UserControl? _deviceContent;
        public UserControl? DeviceContent
        {
            get => _deviceContent;
            set
            {
                _deviceContent = value;
                OnPropertyChanged(nameof(DeviceContent));
            }
        }

        private UserControl? _standardsContent;
        public UserControl? StandardsContent
        {
            get => _standardsContent;
            set
            {
                _standardsContent = value;
                OnPropertyChanged(nameof(StandardsContent));
            }
        }

        private UserControl? _softSkillsContent;
        public UserControl? SoftSkillsContent
        {
            get => _softSkillsContent;
            set
            {
                _softSkillsContent = value;
                OnPropertyChanged(nameof(SoftSkillsContent));
            }
        }

        // Tab 1: Tính toán
        private ObservableCollection<SearchResultItem> _filteredResults = new();
        public ObservableCollection<SearchResultItem> FilteredResults
        {
            get => _filteredResults;
            set
            {
                _filteredResults = value;
                OnPropertyChanged(nameof(FilteredResults));
            }
        }

        // Tab 2: Lựa chọn thiết bị
        private ObservableCollection<DocumentItem> _filteredDeviceResults = new();
        public ObservableCollection<DocumentItem> FilteredDeviceResults
        {
            get => _filteredDeviceResults;
            set
            {
                _filteredDeviceResults = value;
                OnPropertyChanged(nameof(FilteredDeviceResults));
            }
        }

        // Tab 3: Tiêu chuẩn cơ sở
        private ObservableCollection<DocumentItem> _filteredStandardsResults = new();
        public ObservableCollection<DocumentItem> FilteredStandardsResults
        {
            get => _filteredStandardsResults;
            set
            {
                _filteredStandardsResults = value;
                OnPropertyChanged(nameof(FilteredStandardsResults));
            }
        }

        // Tab 4: Bộ kỹ năng mềm
        private ObservableCollection<DocumentItem> _filteredSoftSkillsResults = new();
        public ObservableCollection<DocumentItem> FilteredSoftSkillsResults
        {
            get => _filteredSoftSkillsResults;
            set
            {
                _filteredSoftSkillsResults = value;
                OnPropertyChanged(nameof(FilteredSoftSkillsResults));
            }
        }
        // Tab 1: Tính toán - Load UserControls
        public List<SearchResultItem> AllData { get; } = new()
        {
            new SearchResultItem { Title = "Tính chọn động cơ băng tải con lăn", ViewType = typeof(RollerConveyor) },
            new SearchResultItem { Title = "Tính chọn động cơ băng tải belt", ViewType = typeof(BeltConveyor) },
            new SearchResultItem { Title = "Lực xy lanh", ViewType = typeof(CylinderForce) },
            new SearchResultItem { Title = "Tính tốc độ băng tải belt", ViewType = typeof(ConveyorSpeed) },
            new SearchResultItem { Title = "Tính tốc độ băng tải con lăn", ViewType = typeof(RollerConveyorSpeed) },
            new SearchResultItem { Title = "Tính toán băng tải sấy", ViewType = typeof(DryingConveyor) },
        };

        // Tab 2: Lựa chọn thiết bị (Phần II) - Load Word files
        public List<DocumentItem> DeviceSelectionData { get; } = new()
        {
            new DocumentItem { Title = "Tính chọn belt", FilePath = "Resources/DeviceSelection/TinhChonBelt.docx" },
            new DocumentItem { Title = "Tính chiều dài belt", FilePath = "Resources/DeviceSelection/TinhChieuDaiBelt.docx" },
            new DocumentItem { Title = "Tính chọn vật liệu", FilePath = "Resources/DeviceSelection/TinhChonVatLieu.docx" },
            new DocumentItem { Title = "Tổng quan các loại động cơ", FilePath = "Resources/DeviceSelection/TongQuanDongCo.docx" },
            new DocumentItem { Title = "Tổng quan các loại sensor", FilePath = "Resources/DeviceSelection/TongQuanSensor.docx" },
            new DocumentItem { Title = "Tổng quan các loại xy lanh khí nén", FilePath = "Resources/DeviceSelection/TongQuanXyLanh.docx" },
            new DocumentItem { Title = "Tổng quan về Vision", FilePath = "Resources/DeviceSelection/TongQuanVision.docx" },
            new DocumentItem { Title = "Tổng quan về Robot", FilePath = "Resources/DeviceSelection/TongQuanRobot.docx" }
        };

        // Tab 3: Tiêu chuẩn cơ sở (Phần III) - Load Word files
        public List<DocumentItem> StandardsData { get; } = new()
        {
            new DocumentItem { Title = "Tiêu chuẩn dung sai chung", FilePath = "Resources/Standards/TieuChuanDungSaiChung.docx" },
            new DocumentItem { Title = "Tiêu chuẩn dung sai chân gấp", FilePath = "Resources/Standards/TieuChuanDungSaiChanGap.docx" },
            new DocumentItem { Title = "Tiêu chuẩn phối ống trục", FilePath = "Resources/Standards/TieuChuanPhoiOngTruc.docx" },
            new DocumentItem { Title = "Tiêu chuẩn con lăn Intech", FilePath = "Resources/Standards/TieuChuanConLanIntech.docx" },
            new DocumentItem { Title = "Tiêu chuẩn màu sơn", FilePath = "Resources/Standards/TieuChuanMauSon.docx" },
            new DocumentItem { Title = "Tiêu chuẩn hàn", FilePath = "Resources/Standards/TieuChuanHan.docx" },
            new DocumentItem { Title = "Tiêu chuẩn an toàn", FilePath = "Resources/Standards/TieuChuanAnToan.docx" },
            new DocumentItem { Title = "Tiêu chuẩn bụi lông", FilePath = "Resources/Standards/TieuChuanBuiLong.xlsx" }
        };

        // Tab 4: Bộ kỹ năng mềm (Phần IV) - Load Word files
        public List<DocumentItem> SoftSkillsData { get; } = new()
        {
            new DocumentItem { Title = "Hướng dẫn Download một số thiết bị", FilePath = "Resources/SoftSkills/HuongDanDownload.docx" },
            new DocumentItem { Title = "Hướng dẫn đi khảo sát", FilePath = "Resources/SoftSkills/HuongDanKhaoSat.docx" },
            new DocumentItem { Title = "Hướng dẫn lập kế hoạch", FilePath = "Resources/SoftSkills/HuongDanLapKeHoach.pptx" },
            new DocumentItem { Title = "Tính bày bản vẽ và tính tế trong thiết kế", FilePath = "Resources/SoftSkills/TinhBayBanVe.docx" },
            new DocumentItem { Title = "Kỹ năng giao tiếp và thuyết trình", FilePath = "Resources/SoftSkills/KyNangGiaoTiep.docx" },
            new DocumentItem { Title = "Kỹ năng tìm nguyên nhân gốc rễ 5why ?", FilePath = "Resources/SoftSkills/KyNangTimNguyenNhan.docx" }
        };
        // Tab 1: Tính toán - Load UserControl
        public ICommand SelectItemCommand => new DelegateCommand<SearchResultItem>(item =>
        {
            // Tạo instance UserControl từ ViewType
            if (item?.ViewType != null)
            {
                CalculationContent = (UserControl?)Activator.CreateInstance(item.ViewType);
                // Close search results after selection
                IsSearchResultsVisible = false;
                SearchText = "";

                SetContentTitle(item.Title);
            }
        });

        // Tab 2: Lựa chọn thiết bị - Convert and load PDF
        public ICommand SelectDeviceDocumentCommand => new DelegateCommand<DocumentItem>(async item =>
        {
            if (item != null && !string.IsNullOrEmpty(item.FilePath))
            {
                await LoadDocumentAsPdfAsync(item.FilePath, item.Title, "Device");
                IsDeviceResultsVisible = false;
                DeviceSearchText = item.Title;

                SetContentTitle(item.Title);
            }
        });

        // Tab 3: Tiêu chuẩn cơ sở - Convert and load PDF
        public ICommand SelectStandardsDocumentCommand => new DelegateCommand<DocumentItem>(async item =>
        {
            if (item != null && !string.IsNullOrEmpty(item.FilePath))
            {
                await LoadDocumentAsPdfAsync(item.FilePath, item.Title, "Standards");
                // Keep list visible and set search text to selected item title
                StandardsSearchText = item.Title;

                SetContentTitle(item.Title);
            }
        });

        // Tab 4: Bộ kỹ năng mềm - Convert and load PDF
        public ICommand SelectSoftSkillsDocumentCommand => new DelegateCommand<DocumentItem>(async item =>
        {
            if (item != null && !string.IsNullOrEmpty(item.FilePath))
            {
                await LoadDocumentAsPdfAsync(item.FilePath, item.Title, "SoftSkills");
                // Keep list visible and set search text to selected item title
                SoftSkillsSearchText = item.Title;

                SetContentTitle(item.Title);
            }
        });
        public ICommand Option1Command => new DelegateCommand(() =>
        {
            try
            {
                var filePath = System.IO.Path.GetFullPath(System.IO.Path.Combine("Resources", "PlansWebinterfaceAMR.docx"));
                if (System.IO.File.Exists(filePath))
                {
                    var guidePage = new Documentation.GuidePage(filePath);
                    guidePage.Show();
                }
                else
                {
                    System.Windows.MessageBox.Show($"File not found: {filePath}", "Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error opening file: {ex.Message}", "Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        });

        public ICommand Option2Command => new DelegateCommand(() =>
        {
            try
            {
                var filePath = System.IO.Path.GetFullPath(System.IO.Path.Combine("Resources", "PlansWebinterfaceAMR2.docx"));
                if (System.IO.File.Exists(filePath))
                {
                    var guidePage = new Documentation.GuidePage(filePath);
                    guidePage.Show();
                }
                else
                {
                    System.Windows.MessageBox.Show($"File not found: {filePath}", "Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error opening file: {ex.Message}", "Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        });
        // Tab 1: Tính toán
        private void FilterResults()
        {
            Debug.WriteLine($"FilterResults called. SearchText: '{SearchText}'");
            FilteredResults.Clear();

            // Show all items when search is empty or show filtered results
            var results = string.IsNullOrWhiteSpace(SearchText)
                ? AllData.ToList()
                : AllData.Where(x => x.Title.Contains(SearchText, StringComparison.OrdinalIgnoreCase)).ToList();

            foreach (var item in results)
            {
                FilteredResults.Add(item);
            }
        }

        // Tab 2: Lựa chọn thiết bị
        private void FilterDeviceResults()
        {
            Debug.WriteLine($"FilterDeviceResults called. DeviceSearchText: '{DeviceSearchText}'");
            FilteredDeviceResults.Clear();

            var results = string.IsNullOrWhiteSpace(DeviceSearchText)
                ? DeviceSelectionData.ToList()
                : DeviceSelectionData.Where(x => x.Title.Contains(DeviceSearchText, StringComparison.OrdinalIgnoreCase)).ToList();

            foreach (var item in results)
            {
                FilteredDeviceResults.Add(item);
            }
        }

        // Tab 3: Tiêu chuẩn cơ sở
        private void FilterStandardsResults()
        {
            Debug.WriteLine($"FilterStandardsResults called. StandardsSearchText: '{StandardsSearchText}'");
            FilteredStandardsResults.Clear();

            var results = string.IsNullOrWhiteSpace(StandardsSearchText)
                ? StandardsData.ToList()
                : StandardsData.Where(x => x.Title.Contains(StandardsSearchText, StringComparison.OrdinalIgnoreCase)).ToList();

            foreach (var item in results)
            {
                FilteredStandardsResults.Add(item);
            }
        }

        // Tab 4: Bộ kỹ năng mềm
        private void FilterSoftSkillsResults()
        {
            Debug.WriteLine($"FilterSoftSkillsResults called. SoftSkillsSearchText: '{SoftSkillsSearchText}'");
            FilteredSoftSkillsResults.Clear();

            var results = string.IsNullOrWhiteSpace(SoftSkillsSearchText)
                ? SoftSkillsData.ToList()
                : SoftSkillsData.Where(x => x.Title.Contains(SoftSkillsSearchText, StringComparison.OrdinalIgnoreCase)).ToList();

            foreach (var item in results)
            {
                FilteredSoftSkillsResults.Add(item);
            }
        }

        // Load document as PDF method
        private async Task LoadDocumentAsPdfAsync(string filePath, string title, string tabType)
        {
            try
            {
                Debug.WriteLine($"Loading document as PDF: {filePath}");
                var fullPath = System.IO.Path.GetFullPath(filePath);

                if (!System.IO.File.Exists(fullPath))
                {
                    System.Windows.MessageBox.Show($"File not found: {fullPath}", "Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                    return;
                }

                // Show loading indicator
                var loadingControl = new UserControl();
                var loadingGrid = new Grid();
                var loadingText = new TextBlock
                {
                    Text = $"Converting {title} to PDF...",
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center,
                    FontSize = 16
                };
                loadingGrid.Children.Add(loadingText);
                loadingControl.Content = loadingGrid;

                // Set loading control to appropriate tab
                switch (tabType)
                {
                    case "Device":
                        DeviceContent = loadingControl;
                        break;
                    case "Standards":
                        StandardsContent = loadingControl;
                        break;
                    case "SoftSkills":
                        SoftSkillsContent = loadingControl;
                        break;
                }

                try
                {
                    // Convert to PDF
                    var pdfPath = await OfficeToPdfConverter.ConvertToPdfAsync(fullPath);

                    if (!string.IsNullOrEmpty(pdfPath) && System.IO.File.Exists(pdfPath))
                    {
                        // Create PDF viewer
                        var pdfViewer = new PdfViewerControl();
                        await pdfViewer.LoadPdfAsync(pdfPath);

                        // Set PDF viewer to appropriate tab
                        switch (tabType)
                        {
                            case "Device":
                                DeviceContent = pdfViewer;
                                break;
                            case "Standards":
                                StandardsContent = pdfViewer;
                                break;
                            case "SoftSkills":
                                SoftSkillsContent = pdfViewer;
                                break;
                        }
                    }
                    else
                    {
                        // Conversion failed, show document viewer
                        ShowOfficeNotAvailableMessage(fullPath, title, tabType);
                    }
                }
                catch (InvalidOperationException ex) when (ex.Message.Contains("Microsoft Office"))
                {
                    ShowOfficeNotAvailableMessage(fullPath, title, tabType);
                }
                catch (System.Runtime.InteropServices.COMException)
                {
                    ShowOfficeNotAvailableMessage(fullPath, title, tabType);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error loading document: {ex.Message}", "Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private void ShowOfficeNotAvailableMessage(string filePath, string title, string tabType)
        {
            // Use SimpleDocumentViewer instead of basic message
            var documentViewer = new SimpleDocumentViewer();
            documentViewer.LoadDocument(filePath, title);

            // Set document viewer to appropriate tab
            switch (tabType)
            {
                case "Device":
                    DeviceContent = documentViewer;
                    break;
                case "Standards":
                    StandardsContent = documentViewer;
                    break;
                case "SoftSkills":
                    SoftSkillsContent = documentViewer;
                    break;
            }
        }

        private void OpenWithDefaultApplication(string filePath)
        {
            try
            {
                var processStartInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = filePath,
                    UseShellExecute = true
                };
                System.Diagnostics.Process.Start(processStartInfo);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Cannot open file: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private WindowChrome _chrome;

        public MainWindow()
        {
            InitializeComponent();
            DataContext = this; // Set DataContext to enable binding

            _chrome = new WindowChrome
            {
                CaptionHeight = TitleBarZone.ActualHeight, // sẽ cập nhật động bên dưới
                ResizeBorderThickness = new Thickness(6),
                CornerRadius = new CornerRadius(0),
                GlassFrameThickness = new Thickness(0),
                UseAeroCaptionButtons = false
            };
            WindowChrome.SetWindowChrome(this, _chrome);

            // Đánh dấu các nút là vùng tương tác trong chrome (fix hover/click không ăn ở giữa)
            WindowChrome.SetIsHitTestVisibleInChrome(TitleButtonsPanel, true);
            WindowChrome.SetIsHitTestVisibleInChrome(BtnMin, true);
            WindowChrome.SetIsHitTestVisibleInChrome(BtnMax, true);
            WindowChrome.SetIsHitTestVisibleInChrome(BtnClose, true);

            // Cập nhật CaptionHeight theo chiều cao thực của title bar (fix “vùng chết” trên/dưới)
            TitleBarZone.SizeChanged += (_, __) =>
            {
                _chrome.CaptionHeight = TitleBarZone.ActualHeight;
            };

            Loaded += (_, __) =>
            {
                if (MainTabs?.SelectedItem is TabItem ti)
                {
                    var name = ti.Tag as string ?? ti.Header as string ?? (ti.Header as TextBlock)?.Text ?? ti.Header?.ToString();
                    SetTabTitle(name);
                }
            };

            // (Tuỳ chọn) Kéo cửa sổ khi kéo vào vùng rỗng của TitleBar
            TitleBarZone.MouseLeftButtonDown += TitleBarZone_MouseLeftButtonDown;

            // Đổi icon Maximize/Restore theo WindowState
            StateChanged += (_, __) => UpdateMaxIcon();
            UpdateMaxIcon();

            // Initialize with all data for all tabs - always show all data initially
            FilterResults();
            FilterDeviceResults();
            FilterStandardsResults();
            FilterSoftSkillsResults();

            // Set all lists to be visible by default
            IsSearchResultsVisible = true;
            IsDeviceResultsVisible = true;
            IsStandardsResultsVisible = true;
            IsSoftSkillsResultsVisible = true;

            // Khởi tạo timer kiểm tra license định kỳ
            InitializeLicenseTimer();
        }

        private void TitleBarZone_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            // Không kéo nếu click trên nút
            if (e.OriginalSource is DependencyObject d && FindAncestor<Button>(d) != null) return;

            if (e.ClickCount == 2 && ResizeMode != ResizeMode.NoResize)
                ToggleMaximize();
            else
                try { DragMove(); } catch { }
        }

        private static T FindAncestor<T>(DependencyObject current) where T : DependencyObject
        {
            while (current != null)
            {
                if (current is T t) return t;
                current = VisualTreeHelper.GetParent(current);
            }
            return null;
        }

        private void UpdateMaxIcon()
        {
            // Đổi icon theo trạng thái: Normal -> Maximize, Maximized -> Restore
            MaxIcon.Kind = WindowState == WindowState.Maximized
                ? MaterialDesignThemes.Wpf.PackIconKind.DockWindow
                : MaterialDesignThemes.Wpf.PackIconKind.SquareRoundedOutline;
        }

        // Kéo cửa sổ + double click maximize/restore
        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ClickCount == 2 && ResizeMode != ResizeMode.NoResize)
            {
                ToggleMaximize();
            }
            else
            {
                try { DragMove(); } catch { /* ignore while mouse released */ }
            }
        }

        // Mở menu hệ thống (Alt+Space menu) khi right click trên titlebar
        private void TitleBar_MouseRightButtonUp(object sender, MouseButtonEventArgs e)
        {
            var p = PointToScreen(e.GetPosition(this));
            SystemCommands.ShowSystemMenu(this, p);
        }

        private void Minimize_Click(object sender, RoutedEventArgs e)
            => SystemCommands.MinimizeWindow(this);

        private void MaxRestore_Click(object sender, RoutedEventArgs e)
            => ToggleMaximize();

        private void Close_Click(object sender, RoutedEventArgs e)
            => SystemCommands.CloseWindow(this);

        private void ToggleMaximize()
        {
            if (WindowState == WindowState.Maximized)
                SystemCommands.RestoreWindow(this);
            else
                SystemCommands.MaximizeWindow(this);
        }

        /// <summary>
        /// Khởi tạo timer để kiểm tra license định kỳ
        /// </summary>
        private void InitializeLicenseTimer()
        {
            _licenseCheckTimer = new DispatcherTimer();
            _licenseCheckTimer.Interval = TimeSpan.FromMinutes(30); // Kiểm tra mỗi 30 phút
            _licenseCheckTimer.Tick += LicenseCheckTimer_Tick;
            _licenseCheckTimer.Start();
        }

        /// <summary>
        /// Xử lý sự kiện kiểm tra license định kỳ
        /// </summary>
        private void LicenseCheckTimer_Tick(object sender, EventArgs e)
        {
            var licenseManager = IntechApplication.License.LicenseManager.Instance;

            // Chỉ kiểm tra lại nếu cần thiết
            if (licenseManager.ShouldRevalidate())
            {
                var validationResult = licenseManager.ValidateLicense();

                if (!validationResult.IsValid)
                {
                    // Dừng timer
                    _licenseCheckTimer?.Stop();

                    // Hiển thị thông báo lỗi
                    MessageBox.Show(
                        validationResult.ErrorMessage,
                        "License Error",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);

                    // Đóng ứng dụng
                    Application.Current.Shutdown();
                    return;
                }

                // Hiển thị cảnh báo nếu license sắp hết hạn
                if (validationResult.IsWarning)
                {
                    MessageBox.Show(
                        validationResult.WarningMessage,
                        "License Warning",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                }
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is System.Windows.Controls.TextBox textBox)
            {
                SearchText = textBox.Text;
                Debug.WriteLine($"SearchTextBox_TextChanged: '{SearchText}'");
                FilterResults();
                // List always visible, just filter the content
            }
        }

        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            Debug.WriteLine("SearchTextBox_GotFocus called");
            FilterResults();
            // List always visible
        }

        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            // List stays visible - no need to hide
        }

        // Tab 1: Tính toán
        private void SearchResultsListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ListBox listBox && listBox.SelectedItem is SearchResultItem item)
            {
                Debug.WriteLine($"SearchResultsListBox_SelectionChanged: {item.Title}");
                SelectItemCommand.Execute(item);
                // Keep list visible and clear search text
                SearchText = "";

                // Clear selection to allow selecting the same item again
                listBox.SelectedItem = null;
            }
        }

        // Tab 2: Lựa chọn thiết bị
        private void DeviceSearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is System.Windows.Controls.TextBox textBox)
            {
                DeviceSearchText = textBox.Text;
                Debug.WriteLine($"DeviceSearchTextBox_TextChanged: '{DeviceSearchText}'");
                FilterDeviceResults();
                // List always visible, just filter the content
            }
        }

        private void DeviceSearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            Debug.WriteLine("DeviceSearchTextBox_GotFocus called");
            FilterDeviceResults();
            // List always visible
        }

        private void DeviceSearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            // List stays visible - no need to hide
        }

        private void DeviceSearchResultsListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ListBox listBox && listBox.SelectedItem is DocumentItem item)
            {
                Debug.WriteLine($"DeviceSearchResultsListBox_SelectionChanged: {item.Title}");
                SelectDeviceDocumentCommand.Execute(item);
                // Keep list visible and clear search text
                DeviceSearchText = item.Title;

                listBox.SelectedItem = null;
            }
        }

        // Tab 3: Tiêu chuẩn cơ sở
        private void StandardsSearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is System.Windows.Controls.TextBox textBox)
            {
                StandardsSearchText = textBox.Text;
                Debug.WriteLine($"StandardsSearchTextBox_TextChanged: '{StandardsSearchText}'");
                FilterStandardsResults();
                // List always visible, just filter the content
            }
        }

        private void StandardsSearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            Debug.WriteLine("StandardsSearchTextBox_GotFocus called");
            FilterStandardsResults();
            // List always visible
        }

        private void StandardsSearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            // List stays visible - no need to hide
        }

        private void StandardsSearchResultsListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ListBox listBox && listBox.SelectedItem is DocumentItem item)
            {
                Debug.WriteLine($"StandardsSearchResultsListBox_SelectionChanged: {item.Title}");
                SelectStandardsDocumentCommand.Execute(item);
                // Keep list visible and set search text to selected item title
                StandardsSearchText = item.Title;

                listBox.SelectedItem = null;
            }
        }

        // Tab 4: Bộ kỹ năng mềm
        private void SoftSkillsSearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is System.Windows.Controls.TextBox textBox)
            {
                SoftSkillsSearchText = textBox.Text;
                Debug.WriteLine($"SoftSkillsSearchTextBox_TextChanged: '{SoftSkillsSearchText}'");
                FilterSoftSkillsResults();
                // List always visible, just filter the content
            }
        }

        private void SoftSkillsSearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            Debug.WriteLine("SoftSkillsSearchTextBox_GotFocus called");
            FilterSoftSkillsResults();
            // List always visible
        }

        private void SoftSkillsSearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            // List stays visible - no need to hide
        }

        private void SoftSkillsSearchResultsListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ListBox listBox && listBox.SelectedItem is DocumentItem item)
            {
                Debug.WriteLine($"SoftSkillsSearchResultsListBox_SelectionChanged: {item.Title}");
                SelectSoftSkillsDocumentCommand.Execute(item);
                // Keep list visible and set search text to selected item title
                SoftSkillsSearchText = item.Title;

                listBox.SelectedItem = null;
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class InverseBooleanToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? Visibility.Collapsed : Visibility.Visible;
            }
            return Visibility.Visible;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Visibility visibility)
            {
                return visibility == Visibility.Collapsed;
            }
            return false;
        }
    }

    public class DelegateCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool>? _canExecute;

        public DelegateCommand(Action execute, Func<bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object? parameter)
        {
            return _canExecute?.Invoke() ?? true;
        }

        public void Execute(object? parameter)
        {
            _execute();
        }
    }

    public class DelegateCommand<T> : ICommand
    {
        private readonly Action<T> _execute;
        private readonly Func<T, bool>? _canExecute;

        public DelegateCommand(Action<T> execute, Func<T, bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object? parameter)
        {
            if (parameter is T typedParameter)
            {
                return _canExecute?.Invoke(typedParameter) ?? true;
            }
            return false;
        }

        public void Execute(object? parameter)
        {
            if (parameter is T typedParameter)
            {
                _execute(typedParameter);
            }
        }
    }
}
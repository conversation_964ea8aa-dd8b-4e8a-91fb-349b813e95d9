﻿using System.Configuration;
using System.Data;
using System.Windows;
using IntechApplication.License;

namespace IntechApplication
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            // Kiểm tra license trước khi khởi động ứng dụng
            var licenseManager = LicenseManager.Instance;
            var validationResult = licenseManager.ValidateLicense();

            if (!validationResult.IsValid)
            {
                // Hiển thị thông báo lỗi license
                MessageBox.Show(
                    validationResult.ErrorMessage,
                    "License Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);

                // Thoát ứng dụng
                Shutdown();
                return;
            }

            // Hiển thị cảnh báo nếu license sắp hết hạn
            if (validationResult.IsWarning)
            {
                MessageBox.Show(
                    validationResult.WarningMessage,
                    "License Warning",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);
            }

            // Tiếp tục khởi động ứng dụng bình thường
            base.OnStartup(e);
        }
    }

}

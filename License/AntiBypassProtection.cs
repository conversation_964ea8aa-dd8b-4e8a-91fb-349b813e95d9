using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Win32;

namespace IntechApplication.License
{
    /// <summary>
    /// Class chứa các biện pháp bảo vệ chống bypass license
    /// </summary>
    public static class AntiBypassProtection
    {
        private const string REGISTRY_KEY = @"SOFTWARE\IntechApplication\Protection";
        private const string CHECKSUM_VALUE = "AppChecksum";
        private const string LAST_RUN_VALUE = "LastRun";
        private const string RUN_COUNT_VALUE = "RunCount";

        /// <summary>
        /// Kiểm tra tính toàn vẹn của ứng dụng
        /// </summary>
        public static bool VerifyApplicationIntegrity()
        {
            try
            {
                var appPath = System.Reflection.Assembly.GetExecutingAssembly().Location;
                var currentChecksum = CalculateFileChecksum(appPath);
                var storedChecksum = GetStoredChecksum();

                // Nếu chưa có checksum được lư<PERSON>, lưu checksum hiện tại
                if (string.IsNullOrEmpty(storedChecksum))
                {
                    StoreChecksum(currentChecksum);
                    return true;
                }

                // So sánh checksum
                //return currentChecksum == storedChecksum;
                return true;
            }
            catch
            {
                // Nếu có lỗi, cho phép chạy nhưng ghi log
                return true;
            }
        }

        /// <summary>
        /// Kiểm tra thời gian chạy ứng dụng có bất thường không
        /// </summary>
        public static bool VerifyRunTimePattern()
        {
            try
            {
                var lastRun = GetLastRunTime();
                var runCount = GetRunCount();
                var currentTime = DateTime.Now;

                // Cập nhật thời gian chạy hiện tại
                SetLastRunTime(currentTime);
                SetRunCount(runCount + 1);

                // Kiểm tra thời gian không được quay lại quá nhiều
                if (lastRun != DateTime.MinValue && currentTime < lastRun.AddHours(-2))
                {
                    return false; // Thời gian bị quay lại quá nhiều
                }

                // Kiểm tra pattern chạy ứng dụng có bất thường không
                // Ví dụ: nếu chạy quá nhiều lần trong thời gian ngắn
                if (runCount > 100 && (currentTime - lastRun).TotalMinutes < 5)
                {
                    return true; // Có thể đang bị debug hoặc bypass
                }

                return true;
            }
            catch
            {
                return true; // Nếu có lỗi, cho phép chạy
            }
        }

        /// <summary>
        /// Kiểm tra môi trường chạy có đáng ngờ không
        /// </summary>
        public static bool VerifyEnvironment()
        {
            try
            {
                // Kiểm tra debugger
                //if (System.Diagnostics.Debugger.IsAttached)
                //{
                //    return false;
                //}

                // Kiểm tra các process đáng ngờ
                var suspiciousProcesses = new[]
                {
                    //"ollydbg", "x64dbg", "ida", "cheatengine", "processhacker",
                    //"wireshark", "fiddler", "dnspy", "reflexil", "de4dot"
                    "none"
                };

                var processes = System.Diagnostics.Process.GetProcesses();
                foreach (var process in processes)
                {
                    try
                    {
                        var processName = process.ProcessName.ToLower();
                        foreach (var suspicious in suspiciousProcesses)
                        {
                            if (processName.Contains(suspicious))
                            {
                                return false;
                            }
                        }
                    }
                    catch
                    {
                        // Ignore process access errors
                    }
                }

                return true;
            }
            catch
            {
                return true; // Nếu có lỗi, cho phép chạy
            }
        }

        /// <summary>
        /// Tính checksum của file
        /// </summary>
        private static string CalculateFileChecksum(string filePath)
        {
            using (var sha256 = SHA256.Create())
            {
                using (var stream = File.OpenRead(filePath))
                {
                    var hashBytes = sha256.ComputeHash(stream);
                    return Convert.ToBase64String(hashBytes);
                }
            }
        }

        /// <summary>
        /// Lấy checksum đã lưu từ registry
        /// </summary>
        private static string GetStoredChecksum()
        {
            try
            {
                using (var key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY))
                {
                    return key?.GetValue(CHECKSUM_VALUE)?.ToString() ?? string.Empty;
                }
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Lưu checksum vào registry
        /// </summary>
        private static void StoreChecksum(string checksum)
        {
            try
            {
                using (var key = Registry.CurrentUser.CreateSubKey(REGISTRY_KEY))
                {
                    key?.SetValue(CHECKSUM_VALUE, checksum);
                }
            }
            catch
            {
                // Ignore registry errors
            }
        }

        /// <summary>
        /// Lấy thời gian chạy cuối cùng
        /// </summary>
        private static DateTime GetLastRunTime()
        {
            try
            {
                using (var key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY))
                {
                    var value = key?.GetValue(LAST_RUN_VALUE)?.ToString();
                    if (DateTime.TryParse(value, out var lastRun))
                    {
                        return lastRun;
                    }
                }
            }
            catch
            {
                // Ignore registry errors
            }
            return DateTime.MinValue;
        }

        /// <summary>
        /// Lưu thời gian chạy
        /// </summary>
        private static void SetLastRunTime(DateTime time)
        {
            try
            {
                using (var key = Registry.CurrentUser.CreateSubKey(REGISTRY_KEY))
                {
                    key?.SetValue(LAST_RUN_VALUE, time.ToString("O"));
                }
            }
            catch
            {
                // Ignore registry errors
            }
        }

        /// <summary>
        /// Lấy số lần chạy
        /// </summary>
        private static int GetRunCount()
        {
            try
            {
                using (var key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY))
                {
                    var value = key?.GetValue(RUN_COUNT_VALUE)?.ToString();
                    if (int.TryParse(value, out var count))
                    {
                        return count;
                    }
                }
            }
            catch
            {
                // Ignore registry errors
            }
            return 0;
        }

        /// <summary>
        /// Lưu số lần chạy
        /// </summary>
        private static void SetRunCount(int count)
        {
            try
            {
                using (var key = Registry.CurrentUser.CreateSubKey(REGISTRY_KEY))
                {
                    key?.SetValue(RUN_COUNT_VALUE, count.ToString());
                }
            }
            catch
            {
                // Ignore registry errors
            }
        }
    }
}

using System;

namespace IntechApplication.License
{
    /// <summary>
    /// Utility class để tạo RSA key pair cho hệ thống license
    /// Chỉ sử dụng một lần để tạo key, sau đó xóa file này
    /// </summary>
    public static class KeyGenerator
    {
        public static void GenerateAndPrintKeys()
        {
            var (publicKey, privateKey) = RSAHelper.GenerateKeyPair(2048);
            
            Console.WriteLine("=== RSA KEY PAIR ===");
            Console.WriteLine();
            Console.WriteLine("PUBLIC KEY (nhúng vào LicenseManager.cs):");
            Console.WriteLine(publicKey);
            Console.WriteLine();
            Console.WriteLine("PRIVATE KEY (lưu vào LicenseGenerator):");
            Console.WriteLine(privateKey);
            Console.WriteLine();
            Console.WriteLine("QUAN TRỌNG:");
            Console.WriteLine("1. Copy PUBLIC KEY vào LicenseManager.cs");
            Console.WriteLine("2. Lưu PRIVATE KEY vào file private_key.txt trong LicenseGenerator");
            Console.WriteLine("3. Xóa file KeyGenerator.cs này sau khi hoàn thành");
        }
    }
}

using System;
using System.IO;

namespace IntechApplication.License
{
    /// <summary>
    /// Class để test hệ thống license
    /// Chỉ sử dụng trong development, xóa trong production
    /// </summary>
    public static class LicenseSystemTest
    {
        /// <summary>
        /// Test toàn bộ hệ thống license
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("=== TESTING LICENSE SYSTEM ===");
            Console.WriteLine();

            try
            {
                // Test 1: RSA Key Generation
                TestRSAKeyGeneration();

                // Test 2: License Creation and Verification
                TestLicenseCreationAndVerification();

                // Test 3: Machine ID Generation
                TestMachineIdGeneration();

                // Test 4: Anti-Bypass Protection
                TestAntiBypassProtection();

                // Test 5: License Manager
                TestLicenseManager();

                Console.WriteLine("✅ Tất cả tests đã pass!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Test failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        private static void TestRSAKeyGeneration()
        {
            Console.WriteLine("Test 1: RSA Key Generation");
            
            var (publicKey, privateKey) = RSAHelper.GenerateKeyPair(2048);
            
            if (string.IsNullOrEmpty(publicKey) || string.IsNullOrEmpty(privateKey))
            {
                throw new Exception("Failed to generate RSA keys");
            }

            // Test signing and verification
            var testData = "Hello, World!";
            var signature = RSAHelper.SignData(testData, privateKey);
            var isValid = RSAHelper.VerifySignature(testData, signature, publicKey);

            if (!isValid)
            {
                throw new Exception("RSA signature verification failed");
            }

            Console.WriteLine("✅ RSA Key Generation test passed");
        }

        private static void TestLicenseCreationAndVerification()
        {
            Console.WriteLine("Test 2: License Creation and Verification");

            // Tạo license info
            var licenseInfo = new LicenseInfo
            {
                User = "test.user",
                Company = "Test Company",
                IssueDate = DateTime.Now,
                ExpiryDate = DateTime.Now.AddDays(30),
                Version = "1.0",
                Features = new[] { "All" },
                LicenseId = Guid.NewGuid().ToString()
            };

            // Tạo key pair
            var (publicKey, privateKey) = RSAHelper.GenerateKeyPair(2048);

            // Ký license
            var licenseData = licenseInfo.ToJsonString();
            var signature = RSAHelper.SignData(licenseData, privateKey);

            // Tạo license file
            var licenseFile = LicenseFile.Create(licenseInfo, signature);

            // Verify signature
            var isValid = RSAHelper.VerifySignature(licenseFile.LicenseData, licenseFile.Signature, publicKey);
            if (!isValid)
            {
                throw new Exception("License signature verification failed");
            }

            // Test license validity
            if (!licenseInfo.IsValid())
            {
                throw new Exception("License should be valid");
            }

            Console.WriteLine("✅ License Creation and Verification test passed");
        }

        private static void TestMachineIdGeneration()
        {
            Console.WriteLine("Test 3: Machine ID Generation");

            var machineId1 = MachineIdentifier.GetMachineId();
            var machineId2 = MachineIdentifier.GetMachineId();

            if (string.IsNullOrEmpty(machineId1))
            {
                throw new Exception("Machine ID should not be empty");
            }

            if (machineId1 != machineId2)
            {
                throw new Exception("Machine ID should be consistent");
            }

            Console.WriteLine($"✅ Machine ID Generation test passed. ID: {machineId1.Substring(0, 10)}...");
        }

        private static void TestAntiBypassProtection()
        {
            Console.WriteLine("Test 4: Anti-Bypass Protection");

            // Test application integrity
            var integrityResult = AntiBypassProtection.VerifyApplicationIntegrity();
            Console.WriteLine($"Application Integrity: {(integrityResult ? "✅ Pass" : "❌ Fail")}");

            // Test runtime pattern
            var runtimeResult = AntiBypassProtection.VerifyRunTimePattern();
            Console.WriteLine($"Runtime Pattern: {(runtimeResult ? "✅ Pass" : "❌ Fail")}");

            // Test environment
            var environmentResult = AntiBypassProtection.VerifyEnvironment();
            Console.WriteLine($"Environment Check: {(environmentResult ? "✅ Pass" : "⚠️ Warning - Debugger detected")}");

            Console.WriteLine("✅ Anti-Bypass Protection test completed");
        }

        private static void TestLicenseManager()
        {
            Console.WriteLine("Test 5: License Manager");

            // Tạo license file test
            CreateTestLicenseFile();

            // Test license validation
            var licenseManager = LicenseManager.Instance;
            var validationResult = licenseManager.ValidateLicense();

            Console.WriteLine($"License Validation: {(validationResult.IsValid ? "✅ Valid" : "❌ Invalid")}");
            
            if (!validationResult.IsValid)
            {
                Console.WriteLine($"Error: {validationResult.ErrorMessage}");
            }
            else
            {
                Console.WriteLine($"Days remaining: {validationResult.DaysRemaining}");
                Console.WriteLine($"User: {validationResult.LicenseInfo?.User}");
                Console.WriteLine($"Company: {validationResult.LicenseInfo?.Company}");
            }

            // Cleanup
            CleanupTestFiles();

            Console.WriteLine("✅ License Manager test completed");
        }

        private static void CreateTestLicenseFile()
        {
            // Tạo license info test
            var licenseInfo = new LicenseInfo
            {
                User = "test.user",
                Company = "Test Company",
                IssueDate = DateTime.Now,
                ExpiryDate = DateTime.Now.AddDays(30),
                Version = "1.0",
                Features = new[] { "All" },
                LicenseId = Guid.NewGuid().ToString()
            };

            // Sử dụng key test (trong production phải dùng key thật)
            var (publicKey, privateKey) = RSAHelper.GenerateKeyPair(2048);

            // Ký license
            var licenseData = licenseInfo.ToJsonString();
            var signature = RSAHelper.SignData(licenseData, privateKey);

            // Tạo license file
            var licenseFile = LicenseFile.Create(licenseInfo, signature);

            // Lưu vào file
            var appDirectory = AppDomain.CurrentDomain.BaseDirectory;
            var licenseFilePath = Path.Combine(appDirectory, "app.license");
            File.WriteAllText(licenseFilePath, licenseFile.ToJsonString());

            Console.WriteLine($"Created test license file: {licenseFilePath}");
        }

        private static void CleanupTestFiles()
        {
            try
            {
                var appDirectory = AppDomain.CurrentDomain.BaseDirectory;
                var licenseFilePath = Path.Combine(appDirectory, "app.license");
                
                if (File.Exists(licenseFilePath))
                {
                    File.Delete(licenseFilePath);
                    Console.WriteLine("Cleaned up test license file");
                }
            }
            catch
            {
                // Ignore cleanup errors
            }
        }

        /// <summary>
        /// Test performance của hệ thống
        /// </summary>
        public static void TestPerformance()
        {
            Console.WriteLine("=== PERFORMANCE TEST ===");

            var iterations = 100;
            var startTime = DateTime.Now;

            for (int i = 0; i < iterations; i++)
            {
                var licenseManager = LicenseManager.Instance;
                var result = licenseManager.ValidateLicense();
            }

            var endTime = DateTime.Now;
            var totalTime = (endTime - startTime).TotalMilliseconds;
            var avgTime = totalTime / iterations;

            Console.WriteLine($"Validated license {iterations} times");
            Console.WriteLine($"Total time: {totalTime:F2} ms");
            Console.WriteLine($"Average time per validation: {avgTime:F2} ms");
            Console.WriteLine("✅ Performance test completed");
        }
    }
}

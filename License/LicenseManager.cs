using System;
using System.IO;
using System.Windows;
using Microsoft.Win32;
using Newtonsoft.Json;

namespace IntechApplication.License
{
    /// <summary>
    /// Class chính để quản lý license - đọc, xác minh và kiểm tra tính hợp lệ
    /// </summary>
    public class LicenseManager
    {
        private const string LICENSE_FILE_NAME = "app.license";
        private const string REGISTRY_KEY = @"SOFTWARE\IntechApplication";
        private const string USER_REGISTRY_VALUE = "RegisteredUser";
        private const string INSTALL_TIME_VALUE = "InstallTime";

        // Public key được nhúng cứng trong code (chỉ để verify)
        private const string PUBLIC_KEY = "MIIBCgKCAQEAvdqiPlrRAO7TtQ0lqaY6qBQ3LX0B6ISsRtNE/VKn39GIoSI3BqUmTEIz2pPtgqoq0IZcReSATRnJyZapeJY451gwdr4noyxPQr9dumATRUmXAegtxQbWpvAdHRwTgIJD7xFNgS3ezwT5f40uNoSP6sOhJlwSxLJd3Ov/AY/rnVm9YKZArlWhcczTjvbOTkMf0EmcehpXHgqPWqzJ0CTAJ0mYtn5RWC16S5w7V2i7ZBIRZBeNt78kWqhS1IrE2KUUjNDwZIRyxV0zgfeG+0DHNYo7Ic4M0q5YrU6bwd1x34sR9BRJ/22cidLaz4UEpReNrhuWRKfI8QuGzQH5is7zZQIDAQAB";

        private static LicenseManager _instance;
        private LicenseInfo _currentLicense;
        private DateTime _lastCheckTime;
        private string _registeredUser;

        public static LicenseManager Instance => _instance ??= new LicenseManager();

        private LicenseManager()
        {
            LoadRegisteredUser();
        }

        /// <summary>
        /// Kiểm tra license có hợp lệ không
        /// </summary>
        public LicenseValidationResult ValidateLicense()
        {
            try
            {
                // 1. Kiểm tra file license có tồn tại không
                var licenseFilePath = GetLicenseFilePath();
                if (!File.Exists(licenseFilePath))
                {
                    return new LicenseValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = "Không tìm thấy file license. Vui lòng liên hệ admin để được cấp license."
                    };
                }

                // 2. Đọc và parse license file
                var licenseFileContent = File.ReadAllText(licenseFilePath);
                var licenseFile = LicenseFile.FromJsonString(licenseFileContent);

                // 3. Xác minh chữ ký
                if (!RSAHelper.VerifySignature(licenseFile.LicenseData, licenseFile.Signature, PUBLIC_KEY))
                {
                    return new LicenseValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = "License không hợp lệ hoặc đã bị thay đổi."
                    };
                }

                // 4. Parse license info
                var licenseInfo = licenseFile.GetLicenseInfo();

                // 5. Kiểm tra thời gian hết hạn
                if (licenseInfo.IsExpired())
                {
                    return new LicenseValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = $"License đã hết hạn vào {licenseInfo.ExpiryDate:dd/MM/yyyy}. Vui lòng liên hệ admin để gia hạn."
                    };
                }

                // 6. Kiểm tra user binding
                var userValidation = ValidateUser(licenseInfo);
                if (!userValidation.IsValid)
                {
                    return userValidation;
                }

                // 7. Kiểm tra machine ID (nếu có)
                if (!string.IsNullOrEmpty(licenseInfo.MachineId))
                {
                    var currentMachineId = MachineIdentifier.GetMachineId();
                    if (licenseInfo.MachineId != currentMachineId)
                    {
                        return new LicenseValidationResult
                        {
                            IsValid = false,
                            ErrorMessage = "License không được phép sử dụng trên máy này."
                        };
                    }
                }

                // 8. Kiểm tra chống bypass thời gian
                var timeValidation = ValidateSystemTime(licenseInfo);
                if (!timeValidation.IsValid)
                {
                    return timeValidation;
                }

                // 9. Kiểm tra các biện pháp chống bypass
                var antiBypassValidation = ValidateAntiBypass();
                if (!antiBypassValidation.IsValid)
                {
                    return antiBypassValidation;
                }

                // License hợp lệ
                _currentLicense = licenseInfo;
                _lastCheckTime = DateTime.Now;

                return new LicenseValidationResult
                {
                    IsValid = true,
                    LicenseInfo = licenseInfo,
                    DaysRemaining = licenseInfo.DaysRemaining()
                };
            }
            catch (Exception ex)
            {
                return new LicenseValidationResult
                {
                    IsValid = false,
                    ErrorMessage = $"Lỗi khi kiểm tra license: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Kiểm tra và validate user binding
        /// </summary>
        private LicenseValidationResult ValidateUser(LicenseInfo licenseInfo)
        {
            // Nếu chưa có user đăng ký, lưu user từ license
            if (string.IsNullOrEmpty(_registeredUser))
            {
                SaveRegisteredUser(licenseInfo.User);
                _registeredUser = licenseInfo.User;
                return new LicenseValidationResult { IsValid = true };
            }

            // Kiểm tra user có khớp với user đã đăng ký không
            if (_registeredUser != licenseInfo.User)
            {
                return new LicenseValidationResult
                {
                    IsValid = false,
                    ErrorMessage = $"License này được cấp cho user '{licenseInfo.User}' nhưng máy này đã được đăng ký cho user '{_registeredUser}'."
                };
            }

            return new LicenseValidationResult { IsValid = true };
        }

        /// <summary>
        /// Kiểm tra chống bypass thời gian hệ thống
        /// </summary>
        private LicenseValidationResult ValidateSystemTime(LicenseInfo licenseInfo)
        {
            try
            {
                // Lấy thời gian cài đặt từ registry
                var installTime = GetInstallTime();

                // Nếu chưa có thời gian cài đặt, lưu thời gian hiện tại
                if (installTime == DateTime.MinValue)
                {
                    SaveInstallTime(DateTime.Now);
                    return new LicenseValidationResult { IsValid = true };
                }

                // Kiểm tra thời gian hệ thống không được quay lại trước thời gian cài đặt
                if (DateTime.Now < installTime.AddDays(-1)) // Cho phép sai lệch 1 ngày
                {
                    return new LicenseValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = "Phát hiện thời gian hệ thống bất thường. Vui lòng kiểm tra lại thời gian máy tính."
                    };
                }

                // Kiểm tra thời gian không được quay lại trước lần check cuối
                if (_lastCheckTime != DateTime.MinValue && DateTime.Now < _lastCheckTime.AddMinutes(-5))
                {
                    return new LicenseValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = "Phát hiện thời gian hệ thống bị thay đổi bất thường."
                    };
                }

                return new LicenseValidationResult { IsValid = true };
            }
            catch
            {
                // Nếu có lỗi, cho phép tiếp tục nhưng ghi log
                return new LicenseValidationResult { IsValid = true };
            }
        }

        /// <summary>
        /// Lấy đường dẫn file license
        /// </summary>
        private string GetLicenseFilePath()
        {
            var appDirectory = AppDomain.CurrentDomain.BaseDirectory;
            return Path.Combine(appDirectory, LICENSE_FILE_NAME);
        }

        /// <summary>
        /// Load registered user từ registry
        /// </summary>
        private void LoadRegisteredUser()
        {
            try
            {
                using (var key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY))
                {
                    _registeredUser = key?.GetValue(USER_REGISTRY_VALUE)?.ToString();
                }
            }
            catch
            {
                _registeredUser = null;
            }
        }

        /// <summary>
        /// Lưu registered user vào registry
        /// </summary>
        private void SaveRegisteredUser(string user)
        {
            try
            {
                using (var key = Registry.CurrentUser.CreateSubKey(REGISTRY_KEY))
                {
                    key?.SetValue(USER_REGISTRY_VALUE, user);
                }
            }
            catch
            {
                // Ignore registry errors
            }
        }

        /// <summary>
        /// Lấy thời gian cài đặt từ registry
        /// </summary>
        private DateTime GetInstallTime()
        {
            try
            {
                using (var key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY))
                {
                    var value = key?.GetValue(INSTALL_TIME_VALUE)?.ToString();
                    if (DateTime.TryParse(value, out var installTime))
                    {
                        return installTime;
                    }
                }
            }
            catch
            {
                // Ignore registry errors
            }
            return DateTime.MinValue;
        }

        /// <summary>
        /// Lưu thời gian cài đặt vào registry
        /// </summary>
        private void SaveInstallTime(DateTime installTime)
        {
            try
            {
                using (var key = Registry.CurrentUser.CreateSubKey(REGISTRY_KEY))
                {
                    key?.SetValue(INSTALL_TIME_VALUE, installTime.ToString("O"));
                }
            }
            catch
            {
                // Ignore registry errors
            }
        }

        /// <summary>
        /// Lấy thông tin license hiện tại
        /// </summary>
        public LicenseInfo GetCurrentLicense()
        {
            return _currentLicense;
        }

        /// <summary>
        /// Kiểm tra license có cần được kiểm tra lại không
        /// </summary>
        public bool ShouldRevalidate()
        {
            // Kiểm tra lại mỗi 30 phút
            return DateTime.Now - _lastCheckTime > TimeSpan.FromMinutes(30);
        }

        /// <summary>
        /// Kiểm tra các biện pháp chống bypass
        /// </summary>
        private LicenseValidationResult ValidateAntiBypass()
        {
            try
            {
                // Kiểm tra tính toàn vẹn ứng dụng
                if (!AntiBypassProtection.VerifyApplicationIntegrity())
                {
                    return new LicenseValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = "Phát hiện ứng dụng đã bị thay đổi. Vui lòng cài đặt lại phiên bản gốc."
                    };
                }

                // Kiểm tra pattern thời gian chạy
                if (!AntiBypassProtection.VerifyRunTimePattern())
                {
                    return new LicenseValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = "Phát hiện thời gian hệ thống bất thường hoặc ứng dụng chạy không bình thường."
                    };
                }

                // Kiểm tra môi trường chạy
                if (!AntiBypassProtection.VerifyEnvironment())
                {
                    return new LicenseValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = "Phát hiện môi trường chạy không an toàn. Vui lòng đóng các công cụ debug/hack trước khi chạy ứng dụng."
                    };
                }

                return new LicenseValidationResult { IsValid = true };
            }
            catch
            {
                // Nếu có lỗi trong quá trình kiểm tra, cho phép tiếp tục
                return new LicenseValidationResult { IsValid = true };
            }
        }
    }
}
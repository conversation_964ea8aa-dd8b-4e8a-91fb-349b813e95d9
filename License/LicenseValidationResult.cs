namespace IntechApplication.License
{
    /// <summary>
    /// Kết quả kiểm tra license
    /// </summary>
    public class LicenseValidationResult
    {
        /// <summary>
        /// License có hợp lệ không
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Thông báo lỗi nếu license không hợp lệ
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// Thông tin license nếu hợp lệ
        /// </summary>
        public LicenseInfo LicenseInfo { get; set; }

        /// <summary>
        /// Số ngày còn lại của license
        /// </summary>
        public int DaysRemaining { get; set; }

        /// <summary>
        /// Có phải là cảnh báo sắp hết hạn không
        /// </summary>
        public bool IsWarning => IsValid && DaysRemaining <= 7;

        /// <summary>
        /// Thông báo cảnh báo nếu sắp hết hạn
        /// </summary>
        public string WarningMessage
        {
            get
            {
                if (!IsWarning) return null;
                
                if (DaysRemaining == 0)
                    return "License sẽ hết hạn trong hôm nay!";
                else if (DaysRemaining == 1)
                    return "License sẽ hết hạn trong 1 ngày!";
                else
                    return $"License sẽ hết hạn trong {DaysRemaining} ngày!";
            }
        }
    }
}

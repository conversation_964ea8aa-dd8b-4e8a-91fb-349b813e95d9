# Hướng Dẫn Setup Hệ Thống License

## Bước 1: Tạo RSA Key Pair

### 1.1. Build License Generator
```bash
cd LicenseGenerator
dotnet build
dotnet run
```

### 1.2. Tạo Key Pair
1. Chọn option "1. Tạo cặp RSA Key mới"
2. Nh<PERSON><PERSON> kích thước key: `2048`
3. <PERSON><PERSON> thống sẽ tạo 2 files:
   - `public_key.txt`
   - `private_key.txt`

### 1.3. Cập Nhật Public Key
1. Mở file `public_key.txt`
2. Copy toàn bộ nội dung
3. Mở file `License/LicenseManager.cs`
4. Thay thế giá trị của `PUBLIC_KEY` constant:

```csharp
private const string PUBLIC_KEY = "YOUR_PUBLIC_KEY_HERE";
```

## Bước 2: Test Hệ Thống

### 2.1. Tạo License Test
1. Trong LicenseGenerator, chọn option "2. Tạo License file"
2. <PERSON>h<PERSON><PERSON> thông tin test:
   ```
   Tên user: test.user
   Tên công ty: Test Company
   Số ngày có hiệu lực: 30
   Phiên bản ứng dụng: 1.0
   Machine ID: (để trống)
   Features: All
   ```

### 2.2. Test Main Application
1. Copy file license vừa tạo vào thư mục gốc của IntechApplication
2. Đổi tên thành `app.license`
3. Build và chạy IntechApplication:
   ```bash
   dotnet build
   dotnet run
   ```
4. Ứng dụng sẽ kiểm tra license khi khởi động

### 2.3. Chạy Unit Tests (Tùy chọn)
Thêm code sau vào `MainWindow.xaml.cs` constructor để test:

```csharp
#if DEBUG
// Chỉ chạy trong debug mode
LicenseSystemTest.RunAllTests();
#endif
```

## Bước 3: Production Setup

### 3.1. Xóa Test Code
1. Xóa hoặc comment out test code
2. Xóa file `License/LicenseSystemTest.cs`
3. Xóa file `License/KeyGenerator.cs`

### 3.2. Bảo Mật Private Key
1. Backup `private_key.txt` ở nơi an toàn
2. Không commit private key vào source control
3. Chỉ admin được truy cập private key

### 3.3. Build Release
```bash
dotnet publish -c Release -r win-x64 --self-contained
```

## Bước 4: Quy Trình Cấp License

### 4.1. Nhận Yêu Cầu
- Tên user (bắt buộc)
- Tên công ty
- Thời hạn sử dụng
- Machine ID (nếu cần ràng buộc máy)

### 4.2. Tạo License
1. Chạy LicenseGenerator
2. Chọn option "2. Tạo License file"
3. Nhập thông tin customer
4. File license sẽ được tạo

### 4.3. Gửi License
1. Gửi file `.license` cho customer
2. Hướng dẫn customer:
   - Copy file vào thư mục ứng dụng
   - Đổi tên thành `app.license`
   - Restart ứng dụng

## Bước 5: Troubleshooting

### 5.1. License Không Hoạt Động
**Kiểm tra**:
1. File `app.license` có tồn tại không?
2. Public key trong code có đúng không?
3. Thời gian hệ thống có chính xác không?

**Debug**:
```csharp
var licenseManager = LicenseManager.Instance;
var result = licenseManager.ValidateLicense();
Console.WriteLine($"Valid: {result.IsValid}");
Console.WriteLine($"Error: {result.ErrorMessage}");
```

### 5.2. Anti-Bypass Warnings
**Nguyên nhân thường gặp**:
- Debugger đang attach
- Thời gian hệ thống bị thay đổi
- File ứng dụng bị modify

**Giải pháp**:
- Tắt debugger khi test production
- Kiểm tra thời gian hệ thống
- Build lại ứng dụng từ source sạch

### 5.3. Registry Errors
**Nguyên nhân**: Không có quyền ghi registry
**Giải pháp**: Chạy ứng dụng với quyền admin

## Bước 6: Maintenance

### 6.1. Backup
- Backup private key định kỳ
- Backup source code
- Lưu trữ thông tin license đã cấp

### 6.2. Monitoring
- Theo dõi license expiry dates
- Chuẩn bị gia hạn trước khi hết hạn
- Monitor customer feedback

### 6.3. Updates
- Khi update ứng dụng, giữ nguyên license system
- Chỉ thay đổi version number trong license
- Test kỹ trước khi release

## Security Checklist

- [ ] Private key được bảo mật
- [ ] Public key được nhúng đúng trong code
- [ ] Test code đã được xóa trong production
- [ ] Anti-bypass protection hoạt động
- [ ] Registry permissions OK
- [ ] License validation hoạt động đúng
- [ ] Error messages không leak thông tin nhạy cảm

## Support

Nếu gặp vấn đề:
1. Kiểm tra logs trong Windows Event Viewer
2. Test với license mới
3. Verify RSA keys
4. Check file permissions
5. Contact development team

---

**Lưu ý**: Hệ thống này được thiết kế cho môi trường offline. Không cần kết nối internet để hoạt động.

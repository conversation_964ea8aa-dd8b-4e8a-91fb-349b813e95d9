﻿<Application x:Class="IntechApplication.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:IntechApplication"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="LightGreen"  SecondaryColor="Lime" />

                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesign2.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <Style TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
                <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="10"/>
                <Setter Property="Width" Value="230"/>
                <Setter Property="Height" Value="40"/>
                <Setter Property="BorderBrush" Value="#406CFF"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Padding" Value="10,0,0,0"/>
                <Setter Property="Margin" Value="0,0,0,5"/>
            </Style>

            <Style TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
                <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="10"/>
                <Setter Property="Width" Value="230"/>
                <Setter Property="Height" Value="40"/>
                <Setter Property="BorderBrush" Value="#406CFF"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Margin" Value="0,0,0,5"/>
            </Style>

            <Style x:Key="MaterialDesignComboBoxToggleButton" BasedOn="{StaticResource MaterialDesignComboBoxToggleButton}" TargetType="ToggleButton">
                <Setter Property="Opacity" Value="0"/>
                <Setter Property="IsHitTestVisible" Value="False"/>
                <Setter Property="Width" Value="0"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="ToggleButton">
                            <materialDesign:PackIcon Kind="ChevronDown" Width="40" Height="40"
                                     VerticalAlignment="Center"/>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
                
            </Style>

            <!-- Define Icons based on Material Icon -->
            <materialDesign:PackIcon x:Key="SettingsIcon" Kind="Cog"/>
            <materialDesign:PackIcon x:Key="NofityIcon" Kind="Bell"/>
            <!-- Tabcontrol color -->
            <SolidColorBrush x:Key="TabSelectedBackgroundBrush"   Color="#FFF"/>
            <!-- xanh nhấn -->
            <SolidColorBrush x:Key="TabSelectedForegroundBrush"   Color="#A33CF2"/>
            <SolidColorBrush x:Key="TabUnselectedBackgroundBrush" Color="#A33CF2"/>
            <!-- trong suốt -->
            <SolidColorBrush x:Key="TabUnselectedForegroundBrush" Color="#FFF"/>
            <!-- xám đậm -->

            <!-- Hover  -->
            <SolidColorBrush x:Key="TabHoverBackgroundBrush"      Color="#142962FF"/>
            <!-- xanh nhạt 8% -->
            <SolidColorBrush x:Key="TabHoverForegroundBrush"      Color="#DD000000"/>
        </ResourceDictionary>
    </Application.Resources>
</Application>

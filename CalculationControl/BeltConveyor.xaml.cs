﻿using DocumentFormat.OpenXml.Vml.Office;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.Windows;
using System.Windows.Controls;

namespace IntechApplication.CalculationControl
{
    /// <summary>
    /// Interaction logic for BeltConveyor.xaml
    /// </summary>
    public partial class BeltConveyor : UserControl, INotifyPropertyChanged
    {
        // region for Inputs
        private double _productMass; // C1 : <PERSON>h<PERSON><PERSON> lượng sản phẩm (W)
        public double ProductMass
        {
            get { return _productMass; }
            set
            {
                _productMass = value;
                OnPropertyChanged(nameof(ProductMass));
                UpdateDriveRollerDiameterOptions();
                OnPropertyChanged(nameof(RequiredTorqueHgtNegativeShaft));
                OnPropertyChanged(nameof(RequiredTorqueHgtPositiveShaft));
            }
        }
        private double _conveyorLength; // C2 : <PERSON><PERSON><PERSON> dà<PERSON> băng tải (L)
        public double ConveyorLength
        {
            get { return _conveyorLength; }
            set
            {
                _conveyorLength = value;
                OnPropertyChanged(nameof(ConveyorLength));
                OnPropertyChanged(nameof(BeltFrictionCoefficient));
                OnPropertyChanged(nameof(RequiredTorqueHgtPositiveShaft));
            }
        }
        private double _externalForce; // C4 : Lực tác động bên ngoài (F)
        public double ExternalForce
        {
            get { return _externalForce; }
            set
            {
                _externalForce = value;
                OnPropertyChanged(nameof(ExternalForce));
                OnPropertyChanged(nameof(RequiredTorqueHgtNegativeShaft));
                OnPropertyChanged(nameof(RequiredTorqueHgtPositiveShaft));
            }
        }
        private double _productSpeedOnConveyor; // C5 : Tốc độ sản phẩm trên băng tải (V)
        public double ProductSpeedOnConveyor
        {
            get { return _productSpeedOnConveyor; }
            set
            {
                _productSpeedOnConveyor = value;
                OnPropertyChanged(nameof(ProductSpeedOnConveyor));
                CalculateNegativeTargetTransmissionRatio();
                OnPropertyChanged(nameof(NegativeTargetTransmissionRatio));
                OnPropertyChanged(nameof(TargetTransmissionRatioOptions));
                OnPropertyChanged(nameof(GearPair));
                OnPropertyChanged(nameof(RequiredTorqueHgtPositiveShaft));
            }
        }
        // endregion for Inputs

        // Region for Positive Outputs
        public double SelectedTargetTransmissionRatio
        {
            get { return _selectedTargetTransmissionRatio; }
            set
            {
                _selectedTargetTransmissionRatio = value;
                OnPropertyChanged(nameof(SelectedTargetTransmissionRatio));
                CalculateNegativeTargetTransmissionRatio();
                OnPropertyChanged(nameof(GearPair));
                OnPropertyChanged(nameof(InverterControlFrequency));
                OnPropertyChanged(nameof(HgtPositiveShaftOutputSpeed));
                OnPropertyChanged(nameof(PositiveCalculatedMotorPower));
                OnPropertyChanged(nameof(RequiredTorqueHgtPositiveShaft));
                OnPropertyChanged(nameof(RequiredPositiveMotorPower));
            }
        }
        private int _selectedPositiveDriveRollerDiameter;
        public int SelectedPositiveDriveRollerDiameter
        {
            get { return _selectedPositiveDriveRollerDiameter; }
            set
            {
                _selectedPositiveDriveRollerDiameter = value;
                OnPropertyChanged(nameof(SelectedPositiveDriveRollerDiameter));
                OnPropertyChanged(nameof(TargetTransmissionRatioOptions));
                OnPropertyChanged(nameof(GearPair));
                OnPropertyChanged(nameof(InverterControlFrequency));
                OnPropertyChanged(nameof(RequiredTorqueHgtPositiveShaft));
                OnPropertyChanged(nameof(PositiveLookupPowerByDiameter));
                OnPropertyChanged(nameof(RequiredPositiveMotorPower));
            }
        }

        // Region for Negative Outputs

        private int _selectedNegativeDriveRollerDiameter;
        public int SelectedNegativeDriveRollerDiameter
        {
            get { return _selectedNegativeDriveRollerDiameter; }
            set
            {
                _selectedNegativeDriveRollerDiameter = value;
                OnPropertyChanged(nameof(SelectedNegativeDriveRollerDiameter));
                CalculateNegativeTargetTransmissionRatio();
                OnPropertyChanged(nameof(NegativeTargetTransmissionRatio));
                OnPropertyChanged(nameof(HgtNegativeShaftOutputSpeed));
                OnPropertyChanged(nameof(NegativeInverterControlFrequency));
                OnPropertyChanged(nameof(RequiredTorqueHgtNegativeShaft));
                OnPropertyChanged(nameof(NegativeLookupPowerByDiameter));
                OnPropertyChanged(nameof(NegativeCalculatedMotorPower));
                OnPropertyChanged(nameof(NegativeRequiredMotorPower));
            }
        }
        //Properties tính toán tự động
        private double _selectedBeltFrictionCoefficient;
        public double BeltFrictionCoefficient //C3 : Hệ số ma sát belt µ selected based on ConveyorLength
        {
            get
            {
                return _selectedBeltFrictionCoefficient;
            }
            set
            {
                _selectedBeltFrictionCoefficient = value;
                OnPropertyChanged(nameof(BeltFrictionCoefficient));
                OnPropertyChanged(nameof(RequiredTorqueHgtNegativeShaft));
                OnPropertyChanged(nameof(RequiredTorqueHgtPositiveShaft));
            }
        }

        public double[] BeltFrictionCoefficientOps // C3 : Hệ số ma sát belt µ options
        {
            get
            {
                return [0.3, 0.4, 0.5];
            }
        }

        //Phần Negative
        public double NegativeTargetTransmissionRatio //C7 : Tỉ số truyền
        {
            get { return CalculateNegativeTargetTransmissionRatio(); }
        }
        public double NegativeInverterControlFrequency //C8 Tần số điều khiển biến tần(Hz), max 50Hz(nên từ 30-50)
        {
            get
            {
                double denominator = Math.PI * SelectedNegativeDriveRollerDiameter * 1.4 / ProductSpeedOnConveyor;
                return denominator == 0 ? 0 : 50 * NegativeTargetTransmissionRatio / denominator;
            }
        }
        public double HgtNegativeShaftOutputSpeed // C9 : Tốc độ đầu ra HGT cốt âm n
        {
            get
            {
                return 1400 / NegativeTargetTransmissionRatio;
            }
        }
        public double RequiredTorqueHgtNegativeShaft // C10 : Moment cần thiết HGT cốt âm
        {
            get
            {
                double D = SelectedNegativeDriveRollerDiameter;
                double F = ExternalForce;
                double µ = BeltFrictionCoefficient;
                double m = ProductMass;

                return (1.5 * 0.5 * D * (F + µ * m * 9.81)) / 1000;
            }
        }
        public double NegativeLookupPowerByDiameter // Tương đương D2 trong excel
        {
            get
            {
                double d = SelectedNegativeDriveRollerDiameter;

                // Lọc tất cả công suất có chứa đường kính D
                var matchedPowers = Constants.PowerTables
                    .Where(kvp => kvp.Value.Contains((int)d))
                    .Select(kvp => kvp.Key)
                    .ToList();

                return matchedPowers.Count == 0 ? 0 : matchedPowers.Min(); // Như hàm MIN() trong Excel
            }
        }
        public double NegativeCalculatedMotorPower //D4
        {
            get
            {
                if (NegativeTargetTransmissionRatio == 0) return 0;

                double raw = 0.16 * RequiredTorqueHgtNegativeShaft / (0.62 * NegativeTargetTransmissionRatio);

                return Constants.MotorPowersKW
                                .Where(p => p >= raw)
                                .DefaultIfEmpty(999)
                                .Min(); // ✔ Excel MATCH(...,-1)
            }
        }
        public string NegativeRequiredMotorPower //C11 : Công suất động cơ cần thiết cốt âm
        {
            get
            {
                double d = SelectedNegativeDriveRollerDiameter;     // $C$6
                double t = NegativeTargetTransmissionRatio;         // $C$7
                double m = RequiredTorqueHgtNegativeShaft;          // $C$10
                double d4 = NegativeCalculatedMotorPower;                   // D4
                string e4 = $"DC{d4}KW";                            // E4 - tên bảng dò ngầm

                Debug.WriteLine($"{d},{t},{m},{d4},{e4}");
                // Theo điều kiện Excel C11
                if (d == 141)
                {
                    if (t >= 5 && t <= 10 && d4 > 0.75 && d4 <= 1.5) return "1.5KW";
                    if (t >= 5 && t <= 10 && d4 > 1.5 && d4 <= 2.2) return "2.2KW";
                    if (t > 10 && t <= 30 && d4 > 0.75 && d4 <= 1.5) return "1.5KW";
                    if ((t >= 5 && t <= 10 && d4 > 2.2) || (t > 30 && d4 > 0.75)) return "Chọn đường kính khác";
                    if (d4 <= 0.75) return "0.75KW";
                }

                if (d == 168)
                {
                    if ((t >= 5 && t <= 10 && d4 > 0.75 && d4 <= 1.5) ||
                        (t >= 5 && t <= 10 && d4 > 1.5 && d4 <= 2.2) ||
                        (t > 10 && t <= 30 && d4 > 0.75 && d4 <= 1.5))
                        return "Chọn lại đường kính khác";

                    if ((t > 10 && t <= 30 && d4 > 1.5 && d4 <= 2.2) ||
                        (t > 30 && d4 > 1.5 && d4 <= 2.2))
                        return "2.2KW";

                    if (t > 30 && d4 > 0.75 && d4 <= 1.5) return "1.5KW";
                    if (d4 <= 1.5) return "1.5KW";
                }

                if (d < 141 && d4 <= NegativeLookupPowerByDiameter)
                {
                    return $"{d4}KW";
                }

                // Nếu tra theo bảng thất bại → trả fallback
                return "Chọn đường kính khác";
            }
        }
        //Phần Positive

        public double[] TargetTransmissionRatioOptions // C12 : Tỉ số truyền
        {
            get
            {
                // Tính lookup_value
                double lookupValue = (Math.PI * SelectedPositiveDriveRollerDiameter * 1.4 / ProductSpeedOnConveyor) * (13.0 / 23.0);

                // Tính row_offset (tương đương MATCH(..., 1))
                int rowOffset;
                if (lookupValue <= 10)
                {
                    rowOffset = Constants.TransmissionRatios.TakeWhile(x => x <= lookupValue).Count();
                }
                else
                {
                    rowOffset = Constants.TransmissionRatios.TakeWhile(x => x <= lookupValue).Count() - 1;
                }

                // Tính height (tương đương COUNTIFS)
                double upperBound = lookupValue * (23.0 / 13.0);
                int height = Constants.TransmissionRatios.Count(x => x >= lookupValue && x <= upperBound) +
                             (lookupValue <= 10 ? 1 : 2);

                // Tạo dải động (tương đương OFFSET)
                // Giả định DataColumnB là TransmissionRatios, nên lấy trực tiếp từ TransmissionRatios
                if (rowOffset >= 0 && rowOffset + height <= Constants.TransmissionRatios.Count)
                {
                    return Constants.TransmissionRatios.Skip(rowOffset).Take(height).ToArray();
                }
                else
                {
                    return new double[0]; // Trả về mảng rỗng nếu vượt quá giới hạn
                }
            }
        }
        public string GearPair //C14 : Cặp nhông
        {
            get
            {
                // Tính lookup_value
                double denominator = Math.PI * SelectedPositiveDriveRollerDiameter * 1.4 / ProductSpeedOnConveyor;
                double lookupValue = denominator / SelectedTargetTransmissionRatio;

                // Kiểm tra điều kiện AND
                double ratioCheck = SelectedTargetTransmissionRatio / denominator;
                double minRatio = 13.0 / 23.0; // ≈ 0.5652
                double maxRatio = 23.0 / 13.0; // ≈ 1.7692

                if (ratioCheck >= minRatio && ratioCheck <= maxRatio)
                {
                    // Tìm cặp nhông phù hợp trong GearPairs
                    var matchedGear = Constants.GearPairs
                        .Where(kv => kv.Value <= lookupValue)
                        .OrderByDescending(kv => kv.Value)
                        .FirstOrDefault();

                    return matchedGear.Key ?? "Không tìm thấy tỷ số truyền"; // Trả về khóa (string) hoặc thông báo nếu không tìm thấy
                }
                else if (ratioCheck > maxRatio)
                {
                    return "Chọn tỉ số truyền khác"; // Chuỗi
                }
                else // ratioCheck < minRatio
                {
                    return "13/23"; // Chuỗi
                }
            }
        }
        public string InverterControlFrequency // C15: Tần số điều khiển biến tần (Hz), max 50Hz (nên từ 30-50)
        {
            get
            {
                // Kiểm tra GearPair có trong GearPairs không
                if (!Constants.GearPairs.ContainsKey(GearPair))
                {
                    return "GearPair không hợp lệ"; // Xử lý trường hợp GearPair không tồn tại
                }

                // Lấy giá trị từ GearPairs dựa trên GearPair
                double gearValue = Constants.GearPairs[GearPair];

                // Tính giá trị denominator
                double denominator = Math.PI * SelectedPositiveDriveRollerDiameter * 1.4 / ProductSpeedOnConveyor;

                // Tính giá trị kiểm tra
                double calculatedValue = 50 * SelectedTargetTransmissionRatio * gearValue / denominator;

                // Kiểm tra điều kiện
                if (calculatedValue < 30)
                {
                    return "Chọn tỉ số truyền khác";
                }
                else
                {
                    return calculatedValue.ToString("F2"); // Trả về giá trị dưới dạng chuỗi
                }
            }
        }
        public double HgtPositiveShaftOutputSpeed // C16 : Tốc độ đầu ra HGT cốt dương n = 1400/ tỉ số truyền
        {
            get
            {
                return 1400 / SelectedTargetTransmissionRatio;
            }
        }
        public double RequiredTorqueHgtPositiveShaft
        {
            get
            {
                double gearValue = 0;
                try
                {
                    gearValue = Constants.GearPairs[GearPair];
                }
                catch (KeyNotFoundException)
                {
                    return 0; // Trả về 0 nếu GearPair không hợp lệ
                }
                double denominatorCondition = Math.PI * SelectedPositiveDriveRollerDiameter * 1.4 / ProductSpeedOnConveyor;
                double conditionValue = SelectedTargetTransmissionRatio / denominatorCondition;

                double baseValue = 1.5 * 0.5 * SelectedPositiveDriveRollerDiameter *
                                 (ExternalForce + BeltFrictionCoefficient * ProductMass * 9.81) / 1000;

                Debug.WriteLine($"{gearValue},{denominatorCondition},{conditionValue},{baseValue}");

                return baseValue / gearValue;
            }
        }
        public double PositiveCalculatedMotorPower // D5
        {
            get
            {
                if (SelectedTargetTransmissionRatio == 0) return 0;
                double raw = 0.16 * RequiredTorqueHgtPositiveShaft / (0.62 * SelectedTargetTransmissionRatio);
                return Constants.MotorPowersKW
                                .Where(p => p >= raw)
                                .DefaultIfEmpty(999)
                                .Min(); // ✔ Excel MATCH(...,-1)
            }
        }
        //D3
        public double PositiveLookupPowerByDiameter // Tương đương D3 trong excel
        {
            get
            {
                double d = SelectedPositiveDriveRollerDiameter;
                // Lọc tất cả công suất có chứa đường kính D
                var matchedPowers = Constants.PowerTables
                    .Where(kvp => kvp.Value.Contains((int)d))
                    .Select(kvp => kvp.Key)
                    .ToList();
                return matchedPowers.Count == 0 ? 0 : matchedPowers.Min(); // Như hàm MIN() trong Excel
            }
        }
        public string RequiredPositiveMotorPower
        {
            get
            {
                double d = SelectedPositiveDriveRollerDiameter;
                double t = SelectedTargetTransmissionRatio;
                double m = PositiveCalculatedMotorPower;
                double d3 = PositiveLookupPowerByDiameter;  // Từ C# tương đương với Excel D3
                double d5 = RequiredTorqueHgtPositiveShaft; // Dựa trên d, t, m => dùng match như E5

                // Logic chính xác như Excel
                if (d == 141)
                {
                    if (t >= 5 && t <= 10 && m > 0.75 && m <= 1.5) return "1.5KW";
                    if (t >= 5 && t <= 10 && m > 1.5 && m <= 2.2) return "2.2KW";
                    if (t > 10 && t <= 30 && m > 0.75 && m <= 1.5) return "1.5KW";
                    if ((t >= 5 && t <= 10 && m > 2.2) || (t > 30 && m > 0.75)) return "Chọn lại đường kính khác";
                    if (m <= 0.75) return "0.75KW";
                }

                if (d == 168)
                {
                    if ((t >= 5 && t <= 10 && m > 0.75 && m <= 1.5) ||
                        (t >= 5 && t <= 10 && m > 1.5 && m <= 2.2) ||
                        (t > 10 && t <= 30 && m > 0.75 && m <= 1.5))
                        return "Chọn lại đường kính khác";

                    if ((t > 10 && t <= 30 && m > 1.5 && m <= 2.2) ||
                        (t > 30 && m > 1.5 && m <= 2.2))
                        return "2.2KW";

                    if (t > 30 && m > 0.75 && m <= 1.5) return "1.5KW";

                    if (m <= 0.75) return "Chọn lại đường kính khác";
                }

                if (d < 141 && m <= d3)
                {
                    return $"{d3}KW";
                }

                if (!Constants.PowerTables.TryGetValue(d5, out var diameters) || !diameters.Contains((int)d))
                {
                    return "Chọn đường kính hoặc tỉ số truyền khác";
                }

                return $"{d5}KW";
            }
        }

        private double _selectedTargetTransmissionRatio;
        public ObservableCollection<int> DriveRollerDiameterOptions { get; set; } = new();

        public BeltConveyor()
        {
            InitializeComponent();
            DataContext = this;
        }
        private void CopyText_Click(object sender, RoutedEventArgs e)
        {
            var menuItem = sender as MenuItem;
            var textBlock = (menuItem?.Parent as ContextMenu)?.PlacementTarget as TextBlock;

            if (textBlock != null)
            {
                Clipboard.SetText(textBlock.Text);
            }
        }
        private void UpdateDriveRollerDiameterOptions()
        {
            var source = ProductMass <= 100 ? Constants.ZeroTo100Kg : Constants.UpTo100Kg;

            DriveRollerDiameterOptions.Clear();
            foreach (var item in source)
                DriveRollerDiameterOptions.Add(item);
            UpdateIfNeedChange();
        }
        // Tự động update giá trị đang chọn nếu không có trong danh sách hiện thông báo cần thay đổi lựa chọn
        private void UpdateIfNeedChange()
        {
            if (SelectedNegativeDriveRollerDiameter == 0)
            {
                SelectedNegativeDriveRollerDiameter = DriveRollerDiameterOptions[0];
            }
            else if (!DriveRollerDiameterOptions.Contains(SelectedNegativeDriveRollerDiameter))
            {
                SelectedNegativeDriveRollerDiameter = DriveRollerDiameterOptions[0];
            }
        }

        public double CalculateNegativeTargetTransmissionRatio()
        {
            double value = Math.PI * SelectedNegativeDriveRollerDiameter * 1.4 / ProductSpeedOnConveyor;

            var ratios = Constants.TransmissionRatios;

            // MATCH kiểu 1 (approximate match – tìm giá trị lớn nhất ≤ value)
            var math = ratios
                .Where(r => r <= value)
                .DefaultIfEmpty(ratios.First()) // fallback nếu value nhỏ hơn tất cả
                .Max();
            return math;
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        // Change tooltip if BeltFrictionCoefficient changes
        private void BeltFrictionCoefficient_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // Update the tooltip for BeltFrictionCoefTxt if BeltFrictionCoefficient == 0.3
            if (BeltFrictionCoefficient == 0.3)
            {
                BeltFrictionCoefTxt.ToolTip = "Hệ số ma sát: 0.3 (Chiều dài băng tải < 10m)";
            }
            else if (BeltFrictionCoefficient == 0.4)
            {
                BeltFrictionCoefTxt.ToolTip = "Hệ số ma sát: 0.4 (10m ≤ Chiều dài băng tải < 20m)";
            }
            else if (BeltFrictionCoefficient == 0.5)
            {
                BeltFrictionCoefTxt.ToolTip = "Hệ số ma sát: 0.5 (Chiều dài băng tải ≥ 20m)";
            }
        }
    }
}

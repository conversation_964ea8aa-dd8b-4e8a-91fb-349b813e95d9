﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace IntechApplication.CalculationControl
{
    /// <summary>
    /// Interaction logic for RollerConveyor.xaml
    /// </summary>
    public partial class RollerConveyor : UserControl, INotifyPropertyChanged
    {
        private int _conveyorLength; //Chi<PERSON>u dài băng tải (L1)
        public int ConveyorLength
        {
            get { return _conveyorLength; }
            set
            {
                _conveyorLength = value;
                OnPropertyChanged(nameof(ConveyorLength));
                OnPropertyChanged(nameof(RollerCount));
                OnPropertyChanged(nameof(TotalLoad));
                OnPropertyChanged(nameof(RequiredTorqueHgtPositiveShaft));
                OnPropertyChanged(nameof(RequiredPositiveMotorPower));
            }
        }
        private int _conveyorWidth; //Chiều rộng băng tải(W)
        public int ConveyorWidth
        {
            get { return _conveyorWidth; }
            set
            {
                _conveyorWidth = value;
                OnPropertyChanged(nameof(ConveyorWidth));
                OnPropertyChanged(nameof(RollerCount));
                OnPropertyChanged(nameof(TotalLoad));
                OnPropertyChanged(nameof(RequiredTorqueHgtPositiveShaft));
                OnPropertyChanged(nameof(RequiredPositiveMotorPower));
            }
        }
        private int _selectedRollerDiameter; //Đường kính con lăn (D) được chọn
        public int SelectedRollerDiameter
        {
            get { return _selectedRollerDiameter; }
            set
            {
                _selectedRollerDiameter = value;
                OnPropertyChanged(nameof(SelectedRollerDiameter));
                OnPropertyChanged(nameof(TargetGearRatioOptions));
                OnPropertyChanged(nameof(RollerCount));
                OnPropertyChanged(nameof(TotalLoad));
                OnPropertyChanged(nameof(GearPair));
                OnPropertyChanged(nameof(InverterControlFrequency));
                OnPropertyChanged(nameof(RequiredTorqueHgtPositiveShaft));
                OnPropertyChanged(nameof(RequiredPositiveMotorPower));
            }
        }
        public int[] RollerDiameterOption //Dải đường kính con lăn
        {
            get
            {
                return Constants.RollerDiameters;
            }
        }
        private int _productWeight; //Khối lượng sản phẩm (P) C4
        public int ProductWeight
        {
            get { return _productWeight; }
            set
            {
                _productWeight = value;
                OnPropertyChanged(nameof(ProductWeight));
                OnPropertyChanged(nameof(RollerCount));
                OnPropertyChanged(nameof(TotalLoad));
                OnPropertyChanged(nameof(RequiredTorqueHgtPositiveShaft));
                OnPropertyChanged(nameof(RequiredPositiveMotorPower));
            }
        }
        private double _selectedRollerCenterDistanceB40; //Khoảng cách tâm con lăn (B) được chọn
        public double SelectedRollerCenterDistanceB40
        {
            get { return _selectedRollerCenterDistanceB40; }
            set
            {
                _selectedRollerCenterDistanceB40 = value;
                OnPropertyChanged(nameof(SelectedRollerCenterDistanceB40));
                OnPropertyChanged(nameof(GearPairCount));
                OnPropertyChanged(nameof(RollerCount));
                OnPropertyChanged(nameof(TotalLoad));
                OnPropertyChanged(nameof(RequiredTorqueHgtPositiveShaft));
                OnPropertyChanged(nameof(RequiredPositiveMotorPower));
            }
        }
        public double[] RollerCenterDistanceB40Options //Dải khoảng cách tâm con lăn B40
        {
            get
            {
                return Constants.SprocketB40;
            }
        }
        private int _productSpeed; //Tốc độ sản phẩm (V)
        public int ProductSpeed
        {
            get { return _productSpeed; }
            set
            {
                _productSpeed = value;
                OnPropertyChanged(nameof(ProductSpeed));
                OnPropertyChanged(nameof(TargetGearRatioOptions));
                OnPropertyChanged(nameof(GearPair));
                OnPropertyChanged(nameof(InverterControlFrequency));
                OnPropertyChanged(nameof(RequiredTorqueHgtPositiveShaft));
                OnPropertyChanged(nameof(RequiredPositiveMotorPower));
            }
        }
        private int _motorMountPosition; // Vị trí đặt động cơ (L2)
        public int MotorMountPosition
        {
            get { return _motorMountPosition; }
            set
            {
                _motorMountPosition = value;
                OnPropertyChanged(nameof(MotorMountPosition));
                OnPropertyChanged(nameof(GearPairCount));
                OnPropertyChanged(nameof(RequiredTorqueHgtPositiveShaft));
                OnPropertyChanged(nameof(RequiredPositiveMotorPower));
            }
        }
        //Auto calculate properties
        public double TotalLoad
        {
            get
            {
                double factor = SelectedRollerDiameter switch
                {
                    50 => 1.6 * ConveyorWidth / 500,
                    60 => 1.9 * ConveyorWidth / 500,
                    76 => 2.7 * ConveyorWidth / 500,
                    _ => 0 // hoặc double.NaN để báo lỗi mềm
                };

                return ProductWeight + RollerCount * factor;
            }
        }
        private double _selectedTargetGearRatio;
        public double SelectedTargetGearRatio
        {
            get { return _selectedTargetGearRatio; }
            set
            {
                _selectedTargetGearRatio = value;
                OnPropertyChanged(nameof(SelectedTargetGearRatio));
                OnPropertyChanged(nameof(GearPair));
                OnPropertyChanged(nameof(InverterControlFrequency));
                OnPropertyChanged(nameof(RequiredTorqueHgtPositiveShaft));
                OnPropertyChanged(nameof(RequiredPositiveMotorPower));
            }
        }
        public string GearPair
        {
            get
            {
                double compareValue = SelectedTargetGearRatio / ((Math.PI * SelectedRollerDiameter * 1.4) / ProductSpeed);

                // MATCH(...,-1) ~ chọn "giá trị nhỏ nhất nhưng ≥ compareValue"
                foreach (var pair in Constants.BtclGearPairs.OrderBy(p => p.Value)) // tăng dần
                {
                    if (pair.Value >= compareValue)
                        return pair.Key; // vd: "20/14"
                }

                // Không tìm thấy phần tử ≥ compareValue  -> #N/A -> thông điệp
                return "Chọn tỉ số truyền bé hơn";
            }
        }
        public double InverterControlFrequency
        {
            get
            {
                if (!Constants.BtclGearPairs.TryGetValue(GearPair, out double matchedGearRatio))
                {
                    return 0;
                }

                double denominator = (Math.PI * SelectedRollerDiameter * 1.4) / ProductSpeed;

                return 50 * SelectedTargetGearRatio / matchedGearRatio / denominator;
            }
        }
        public double[] TargetGearRatioOptions
        {
            get
            {
                // Tính lookup_value
                double lookupValue = (Math.PI * SelectedRollerDiameter * 1.4) / ProductSpeed;

                // Tính row_offset (tương đương MATCH(..., 1) - 1)
                int matchIndex = Constants.TransmissionRatios.TakeWhile(x => x <= lookupValue).Count() - 1;
                if (matchIndex < 0) matchIndex = 0; // Đảm bảo không âm

                // Tính height (tương đương COUNTIFS + 2)
                double upperBound = lookupValue * (24.0 / 14.0); // ≈ 1.7143
                int height = Constants.TransmissionRatios.Count(x => x >= lookupValue && x <= upperBound) + 2;
                if (height < 0) height = 0; // Đảm bảo không âm

                // Tính dải động (tương đương OFFSET)
                int startIndex = matchIndex;
                int endIndex = Math.Min(startIndex + height, Constants.TransmissionRatios.Count);
                if (startIndex >= Constants.TransmissionRatios.Count || endIndex <= startIndex)
                {
                    return new double[0]; // Trả về mảng rỗng nếu vượt giới hạn
                }

                return Constants.TransmissionRatios.GetRange(startIndex, endIndex - startIndex).ToArray();
            }
        }
        public int GearPairCount
        {
            get
            {
                if (SelectedRollerCenterDistanceB40 == 0)
                {
                    return 0; // Tránh chia cho 0
                }
                return (int)(MotorMountPosition / SelectedRollerCenterDistanceB40) + 1;
            }
        }
        public int RollerCount
        {
            get
            {
                if (SelectedRollerCenterDistanceB40 == 0)
                {
                    return 0; // Tránh chia cho 0
                }
                return (int)(ConveyorLength / SelectedRollerCenterDistanceB40) + 1;
            }
        }
        public double RequiredTorqueHgtPositiveShaft
        {
            get
            {
                if (!Constants.BtclGearPairs.TryGetValue(GearPair, out double gearRatio))
                {
                    return 0;
                }

                double gravity = 9.81;
                double G3 = 1.0 / Constants.ChainTransmissionEfficiency;

                double numerator = 1.5 * ((gravity * TotalLoad / GearPairCount) * 2 * Constants.FrictionCoefficientPerBearing * SelectedRollerDiameter);
                double bearingLoss = Math.Pow(Constants.BearingEfficiency, RollerCount) * 2;

                double geometricSeriesFactor = -1 + ((1 - Math.Pow(G3, GearPairCount + 1)) / (1 - G3));

                return (numerator / bearingLoss) * geometricSeriesFactor * gearRatio / 1000.0;
            }
        }
        public double RequiredPositiveMotorPower
        {
            get
            {
                double value = 0.1 * RequiredTorqueHgtPositiveShaft / (6.2 * SelectedTargetGearRatio / 10.0);

                foreach (var p in Constants.MotorPowersKW.OrderBy(x => x)) // 0.06 → 3.0
                {
                    if (p >= value && RequiredTorqueHgtPositiveShaft != 0)
                        return p;
                }

                return 0;
            }
        }
        public RollerConveyor()
        {
            InitializeComponent();
            DataContext = this;
        }

        private void CopyText_Click(object sender, RoutedEventArgs e)
        {
            var menuItem = sender as MenuItem;
            var textBlock = (menuItem?.Parent as ContextMenu)?.PlacementTarget as TextBlock;

            if (textBlock != null)
            {
                Clipboard.SetText(textBlock.Text);
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}

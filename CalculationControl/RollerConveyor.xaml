﻿<UserControl x:Class="IntechApplication.CalculationControl.RollerConveyor"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:IntechApplication.CalculationControl"
             xmlns:mD="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="900" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <TextBlock Text="T<PERSON>h chọn động cơ băng tải con lăn" 
               FontSize="24" 
               FontWeight="Bold" 
               HorizontalAlignment="Center" 
               VerticalAlignment="Top" 
               Margin="10"/>
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="10" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="20" />
                <ColumnDefinition Width="2*" />
            </Grid.ColumnDefinitions>
            <!-- Image Placeholder -->
            <GroupBox Header="Mô hình"
              Grid.Column="1"
              Margin="0,0,0,10"
              mD:ElevationAssist.Elevation="Dp6"
              BorderBrush="DarkRed" BorderThickness="1">
                <Image Source="pack://application:,,,/IntechApplication;component/Resources/Images/RollerConveyor.jpg"/>
            </GroupBox>
            <Grid Grid.Column="3">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Input Parameters-->
                <GroupBox Header="Đầu vào"
                  mD:ElevationAssist.Elevation="Dp6" Margin="0,0,10,5"
                  BorderBrush="DarkRed" BorderThickness="1">
                    <Grid Margin="100,10,10,10" HorizontalAlignment="Left" VerticalAlignment="Center">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*" />
                            <ColumnDefinition Width="20" />
                            <ColumnDefinition Width="100" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <!-- Labels -->
                        <StackPanel Grid.Column="0">
                            <StackPanel.Resources>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="15"/>
                                </Style>
                            </StackPanel.Resources>
                            <TextBlock Padding="5" Height="25" Text="Chiều dài băng tải (L1)" />
                            <TextBlock Padding="5" Height="25" Text="Chiều rộng băng tải (W)" />
                            <TextBlock Padding="5" Height="25" Text="Đường kính con lăn (D)" />
                            <TextBlock Padding="5" Height="25" Text="Khối lượng sản phẩm (P)" />
                            <TextBlock Padding="5" Height="25" Text="Tổng trọng tải (m)" />
                            <TextBlock Padding="5" Height="25" Text="Khoảng cách tâm con lăn (Nhông B40)" />
                            <TextBlock Padding="5" Height="25" Text="Tốc độ sản phẩm (v)" />
                            <TextBlock Padding="5" Height="25" Text="Vị trí đặt động cơ (L2)" />
                            <TextBlock Padding="5" Height="25" Text="Tỉ số truyền mục tiêu (i)" />
                        </StackPanel>
                        <!-- Fields -->
                        <StackPanel Grid.Column="2">
                            <StackPanel.Resources>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="15"/>
                                </Style>
                            </StackPanel.Resources>
                            <TextBox FontSize="15" Height="25" Text="{Binding ConveyorLength}"/>
                            <TextBox FontSize="15" Height="25" Text="{Binding ConveyorWidth}"/>
                            <ComboBox x:Name="RollerDiameter"
                                      Height="25"
                                      ItemsSource="{Binding RollerDiameterOption}"
                                      SelectedItem="{Binding SelectedRollerDiameter}"
                                      />
                            <TextBox FontSize="15" Height="25" Text="{Binding ProductWeight}"/>
                            <TextBlock FontSize="15" Height="25" Text="{Binding TotalLoad, StringFormat={}{0:F2} , UpdateSourceTrigger=PropertyChanged}"/>
                            <ComboBox x:Name="RollerCenterDistanceB40"
                                      Height="25"
                                      ItemsSource="{Binding RollerCenterDistanceB40Options}"
                                      SelectedItem="{Binding SelectedRollerCenterDistanceB40}"
                                      />
                            <TextBox FontSize="15" Height="25" Text="{Binding ProductSpeed}"/>
                            <TextBox FontSize="15" Height="25" Text="{Binding MotorMountPosition}"/>
                            <ComboBox Height="25"
                                      x:Name="TargetGearRatio"
                                      SelectedItem="{Binding SelectedTargetGearRatio}"
                                      ItemsSource="{Binding TargetGearRatioOptions}"
                                      />
                        </StackPanel>
                        <!-- Units -->
                        <StackPanel Grid.Column="3" Margin="10,0,0,0">
                            <StackPanel.Resources>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="15"/>
                                </Style>
                            </StackPanel.Resources>
                            <TextBlock Padding="5" Height="25" Text="mm"/>
                            <TextBlock Padding="5" Height="25" Text="mm" />
                            <TextBlock Padding="5" Height="25" Text="mm" />
                            <TextBlock Padding="5" Height="25" Text="Kg" />
                            <TextBlock Padding="5" Height="25" Text="Kg" />
                            <TextBlock Padding="5" Height="25" Text="mm" />
                            <TextBlock Padding="5" Height="25" Text="m/p" />
                            <TextBlock Padding="5" Height="25" Text="mm" />
                        </StackPanel>
                    </Grid>
                </GroupBox>
                <!-- Output -->
                <GroupBox Header="Đầu ra" Grid.Row="1" Margin="0,5,10,10" Padding="0"
                  mD:ElevationAssist.Elevation="Dp6"
                  BorderBrush="DarkRed" BorderThickness="1">
                    <StackPanel Margin="100,0,0,0" VerticalAlignment="Center" HorizontalAlignment="Left">
                        <Grid HorizontalAlignment="Left">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="2*" />
                                <ColumnDefinition Width="20" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <!-- Labels Positive-->
                            <StackPanel Grid.Column="0">
                                <StackPanel.Resources>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontSize" Value="15"/>
                                    </Style>
                                </StackPanel.Resources>
                                <TextBlock Height="25" Text="Số cặp nhông (b)"/>
                                <TextBlock Height="25" Text="Số con lăn (n)" />
                                <TextBlock Height="25" Text="Cặp nhông Động cơ/Quả lô" />
                                <TextBlock Height="25" Text="Tần số điều khiển biến tần (Hz)" />
                                <TextBlock Height="25" Text="Moment cần thiết HGT cốt dương" />
                                <TextBlock Height="25" Text="Công suất động cơ cần thiết" />
                            </StackPanel>
                            <StackPanel Grid.Column="2">
                                <StackPanel.Resources>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontSize" Value="15"/>
                                    </Style>
                                </StackPanel.Resources>
                                <TextBlock Height="25" Text="{Binding GearPairCount, UpdateSourceTrigger=PropertyChanged}">
                                    <TextBlock.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Header="Copy" Click="CopyText_Click" />
                                        </ContextMenu>
                                    </TextBlock.ContextMenu>
                                </TextBlock>
                                <TextBlock Height="25" Text="{Binding RollerCount, UpdateSourceTrigger=PropertyChanged}">
                                    <TextBlock.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Header="Copy" Click="CopyText_Click" />
                                        </ContextMenu>
                                    </TextBlock.ContextMenu>
                                </TextBlock>
                                <TextBlock Height="25" Text="{Binding GearPair, UpdateSourceTrigger=PropertyChanged}" ToolTip="(HGT cốt dương) Động cơ/Quả lô">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Foreground" Value="Black"/>
                                            <Setter Property="FontSize" Value="15"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding GearPair}" Value="Chọn tỉ số truyền bé hơn">
                                                    <Setter Property="Foreground" Value="Red"/>
                                                    <Setter Property="FontWeight" Value="Bold"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                    <TextBlock.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Header="Copy" Click="CopyText_Click" />
                                        </ContextMenu>
                                    </TextBlock.ContextMenu>
                                </TextBlock>
                                <TextBlock Height="25" Text="{Binding InverterControlFrequency, StringFormat={}{0:F2}, UpdateSourceTrigger=PropertyChanged}" ToolTip=" max 50Hz (nên từ 30-50)">

                                    <TextBlock.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Header="Copy" Click="CopyText_Click" />
                                        </ContextMenu>
                                    </TextBlock.ContextMenu>
                                </TextBlock>
                                <TextBlock Height="25" Text="{Binding RequiredTorqueHgtPositiveShaft, StringFormat={}{0:F2}, UpdateSourceTrigger=PropertyChanged}">
                                    <TextBlock.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Header="Copy" Click="CopyText_Click" />
                                        </ContextMenu>
                                    </TextBlock.ContextMenu>
                                </TextBlock>
                                <TextBlock Height="25" Text="{Binding RequiredPositiveMotorPower, UpdateSourceTrigger=PropertyChanged}">
                                    <TextBlock.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Header="Copy" Click="CopyText_Click" />
                                        </ContextMenu>
                                    </TextBlock.ContextMenu>
                                </TextBlock>
                            </StackPanel>
                            <!-- Units -->
                            <StackPanel Grid.Column="3" Margin="10,0,0,0">
                                <StackPanel.Resources>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontSize" Value="15"/>
                                    </Style>
                                </StackPanel.Resources>
                                <TextBlock Padding="5" Height="25"/>
                                <TextBlock Height="25"/>
                                <TextBlock Height="25"/>
                                <TextBlock Height="25" Text="Hz"/>
                                <TextBlock Height="25" Text="N.m" />
                                <TextBlock Height="25" Text="Kw" />
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </GroupBox>
            </Grid>
        </Grid>
    </Grid>
</UserControl>

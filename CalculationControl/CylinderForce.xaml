﻿<UserControl x:Class="IntechApplication.CalculationControl.CylinderForce"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:IntechApplication.CalculationControl"
             xmlns:md="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="750" d:DesignWidth="900">
    <Grid>
        <Grid.Resources>
            <Style TargetType="Border">
                <Setter Property="CornerRadius" Value="20"/>
                <Setter Property="Background" Value="#FFF"/>
            </Style>
        </Grid.Resources>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="10" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="20" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
            <!-- Image Placeholder -->
        <Grid Grid.Column="1" Margin="0,0,0,10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>
                <TextBlock Grid.Row="0" Text="Hướng dẫn chọn hệ số làm việc" FontWeight="Bold" Margin="0,10,0,10"/>
                <Border Grid.Row="1">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Image Grid.Row="0" Margin="0,0,0,10" Source="pack://application:,,,/IntechApplication;component/Resources/Images/CylinderForce.png"/>
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <StackPanel Grid.Column="0" HorizontalAlignment="Center" VerticalAlignment="Center">
                            <Image Height="80" Source="pack://application:,,,/IntechApplication;component/Resources/Images/07.png"/>
                            <Image Height="80" Source="pack://application:,,,/IntechApplication;component/Resources/Images/10.png"/>
                            <Image Height="80" Source="pack://application:,,,/IntechApplication;component/Resources/Images/05.png"/>
                        </StackPanel>
                        <StackPanel Grid.Column="1" HorizontalAlignment="Left" VerticalAlignment="Center">
                            <TextBlock Height="80" Text="Đẩy hoặc kéo vật, nén ép &#x0a;Không sử dụng dẫn hướng&#x0a;Hệ số làm việc = 0.7" FontSize="14"/>
                            <TextBlock Height="80" Text="Đẩy hoặc kéo vật sử dụng&#x0a;dẫn hướng. Ma sát rất nhỏ&#x0a;Hệ số làm việc = 1" FontSize="14"/>
                            <TextBlock Height="80" Text="Đẩy hoặc kéo vật, nén ép &#x0a;Không sử dụng dẫn hướng&#x0a;Hệ số làm việc = 0.5" FontSize="14"/>
                        </StackPanel>
                    </Grid>
                </Grid>
            </Border>
        </Grid>
        <Grid Grid.Column="3">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <Grid.Resources>
                <Style TargetType="TextBlock">
                    <Setter Property="FontSize" Value="16"/>
                    <Setter Property="Height" Value="40"/>
                    <Setter Property="Padding" Value="0,10,0,0"/>
                    <Setter Property="Margin" Value="0,0,0,5"/>
                </Style>
            </Grid.Resources>

            <!-- Input Parameters-->
            <Grid Grid.Row="0" Margin="0,10,10,10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>
                
                <TextBlock Height="21.28" Margin="0,0,0,10" Padding="0" Text="Thông số tính toán" FontWeight="Bold"/>
                <Border Grid.Row="1">
                    <Grid HorizontalAlignment="Center" VerticalAlignment="Center">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*" />
                            <ColumnDefinition Width="20" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <!-- Labels -->
                        <StackPanel Grid.Column="0">
                            
                            <TextBlock Text="Bore Xilanh"/>
                            <TextBlock Text="Áp suất cấp" />
                            <!-- <TextBlock Text="Hệ số làm việc" /> -->
                            <TextBlock Text="Lực tác động" />
                        </StackPanel>
                        <!-- Fields -->
                        <StackPanel Grid.Column="2">
                            <ComboBox ItemsSource="{Binding CylinderDiameters}" SelectedItem="{Binding SelectedFowardCylinderDiameter, Mode=TwoWay}"/>
                            <ComboBox ItemsSource="{Binding SupplyPressures}" SelectedItem="{Binding SelectedFowardSupplyPressure, Mode=TwoWay}"/>
                            <!-- <ComboBox ItemsSource="{Binding WorkingCoefficientOptions}" SelectedItem="{Binding SelectedFowardWorkingCoefficient, Mode=TwoWay}"/> -->
                            <TextBlock Height="40" Padding="10,10,0,0" Text="{Binding ForwardForce}"/>
                        </StackPanel>
                        <!-- Units -->
                        <StackPanel Grid.Column="3" Margin="10,0,0,0">
                            <TextBlock Text="mm"/>
                            <TextBlock Text="Mpa" />
                            <TextBlock Text="N" />
                        </StackPanel>
                    </Grid>
                </Border>
            </Grid>
            <!-- Output -->
            <Grid Grid.Row="1" Margin="0,10,0,10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>
                <TextBlock Height="21.28" Padding="0" FontWeight="Bold" Margin="0,0,0,10" Text="Kết quả tính toán"/>
                <Border Grid.Row="1">
                    <!-- Positive Force -->
                    <Grid Margin="0,5,10,10" VerticalAlignment="Center" HorizontalAlignment="Center">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*" />
                            <ColumnDefinition Width="20" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <!-- Labels Positive-->
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="Lực tác động"/>
                            <TextBlock Text="Áp suất cấp" />
                            <TextBlock Text="Hệ số làm việc" />
                            <TextBlock Text="Bore Xilanh" />
                        </StackPanel>
                        <!-- Fields Positive-->
                        <StackPanel Grid.Column="2">
                            <TextBox Height="40" Padding="10,0,0,0" Text="{Binding BackwardForce}"/>
                            <ComboBox ItemsSource="{Binding SupplyPressures}" SelectedItem="{Binding SelectedBackwardSupplyPressure}"/>
                            <ComboBox ItemsSource="{Binding WorkingCoefficientOptions}" SelectedItem="{Binding SelectedBackwardWorkingCoefficient}"/>
                            <TextBlock Height="40" Padding="10,10,0,0" Text="{Binding BackwardCylinderDiameter}"/>
                        </StackPanel>
                        <!-- Units Positive-->
                        <StackPanel Grid.Column="3" Margin="10,0,0,0">
                            <TextBlock Text="N"/>
                            <TextBlock Text="Mpa"/>
                            <TextBlock />
                            <TextBlock Text="mm"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </Grid>
        </Grid>
    </Grid>
</UserControl>

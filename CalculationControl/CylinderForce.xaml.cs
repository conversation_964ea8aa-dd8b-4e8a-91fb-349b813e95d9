﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace IntechApplication.CalculationControl
{
    /// <summary>
    /// Interaction logic for CylinderForce.xaml
    /// </summary>
    public partial class CylinderForce : UserControl, INotifyPropertyChanged
    {
        // Cylinder Force Properties
        public int[] CylinderDiameters => Constants.CylinderDiameters;
        public double[] SupplyPressures => Constants.SupplyPressures;
        public string[] WorkingCoefficientOptions => Constants.WorkingCoefficient.Keys.ToArray();
        // Forward Properties
        private int _selectedFowardCylinderDiameter;
        public int SelectedFowardCylinderDiameter
        {
            get { return _selectedFowardCylinderDiameter; }
            set
            {
                _selectedFowardCylinderDiameter = value;
                OnPropertyChanged(nameof(SelectedFowardCylinderDiameter));
                OnPropertyChanged(nameof(ForwardForce)); // Update ForwardForce when diameter changes
            }
        }
        
        private double _selectedFowardSupplyPressure;
        public double SelectedFowardSupplyPressure
        {
            get { return _selectedFowardSupplyPressure; }
            set
            {
                _selectedFowardSupplyPressure = value;
                OnPropertyChanged(nameof(SelectedFowardSupplyPressure));
                OnPropertyChanged(nameof(ForwardForce)); // Update ForwardForce when pressure changes
            }
        }
        
        private string _selectedFowardWorkingCoefficient = "Đẩy hoặc kéo vật, nén ép\nKhông sử dụng dẫn hướng (k=0.7)";
        public string SelectedFowardWorkingCoefficient
        {
            get { return _selectedFowardWorkingCoefficient; }
            set
            {
                _selectedFowardWorkingCoefficient = value;
                OnPropertyChanged(nameof(SelectedFowardWorkingCoefficient));
                OnPropertyChanged(nameof(ForwardForce)); // Update ForwardForce when working coefficient changes
            }
        }
        public double ForwardForce
        {
            get 
            {
                double diameter = SelectedFowardCylinderDiameter / 10.0; // Convert mm to cm
                double pressure = SelectedFowardSupplyPressure * 10.0; // Convert Mpa to Pa
                //double workingCoefficient = Constants.WorkingCoefficient[SelectedFowardWorkingCoefficient.ToString()];
                return Math.Ceiling(Math.PI * Math.Pow(diameter / 2, 2) * pressure * 10);
            }
        }

        // Backward Properties

        private double _selectedBackwardSupplyPressure;
        public double SelectedBackwardSupplyPressure
        {
            get { return _selectedBackwardSupplyPressure; }
            set
            {
                _selectedBackwardSupplyPressure = value;
                OnPropertyChanged(nameof(SelectedBackwardSupplyPressure));
                OnPropertyChanged(nameof(BackwardForce)); // Update BackwardForce when pressure changes
                OnPropertyChanged(nameof(BackwardCylinderDiameter));
            }
        }

        private string _selectedBackwardWorkingCoefficient = "Đẩy hoặc kéo vật, nén ép\nKhông sử dụng dẫn hướng (k=0.7)";
        public string SelectedBackwardWorkingCoefficient
        {
            get { return _selectedBackwardWorkingCoefficient; }
            set
            {
                _selectedBackwardWorkingCoefficient = value;
                OnPropertyChanged(nameof(SelectedBackwardWorkingCoefficient));
                OnPropertyChanged(nameof(BackwardForce)); // Update BackwardForce when working coefficient changes
                OnPropertyChanged(nameof(BackwardCylinderDiameter));
            }
        }
        private double _backwardForce;
        public double BackwardForce
        {
            get { return _backwardForce; }
            set
            {
                _backwardForce = value;
                OnPropertyChanged(nameof(BackwardForce));
                OnPropertyChanged(nameof(BackwardCylinderDiameter)); // Update BackwardCylinderDiameter when force changes
            }
        }
        public int BackwardCylinderDiameter
        {
            get
            {
                var workingCoefficient = Constants.WorkingCoefficient[SelectedBackwardWorkingCoefficient.ToString()];
                double denominator = Math.PI * workingCoefficient * SelectedBackwardSupplyPressure;
                double lookupValue = Math.Sqrt((4 * BackwardForce) / denominator);
                var reversedDiameters = Constants.CylinderDiameters.AsEnumerable().Reverse().ToList();
                int matchIndex = reversedDiameters.TakeWhile(x => x >= lookupValue).Count() - 1;
                int originalIndex = Constants.CylinderDiameters.Length - 1 - matchIndex;
                try
                {
                    var result = Constants.CylinderDiameters[originalIndex];
                    return result;
                }
                catch (IndexOutOfRangeException)
                {
                    return 0; // Return 0 if no match found
                }
            }
        }
        public CylinderForce()
        {
            InitializeComponent();
            DataContext = this;
            //    var tasks = new List<TaskItem>
            //    {
            //        new TaskItem
            //        {
            //            Title = "Đẩy hoặc kéo vật, nén ép. Không sử dụng dẫn hướng",
            //            ImagePath = "pack://application:,,,/IntechApplication;component/Resources/Images/07.png",
            //            HeSo = "Hệ số làm việc = 0.7"
            //        },
            //        new TaskItem
            //        {
            //            Title = "Đẩy hoặc kéo vật sử dụng dẫn hướng. Ma sát rất nhỏ",
            //            ImagePath = "pack://application:,,,/IntechApplication;component/Resources/Images/10.png",
            //            HeSo = "Hệ số làm việc = 1"
            //        },
            //        new TaskItem
            //        {
            //            Title = "Đẩy hoặc kéo vật, nén ép. Không sử dụng dẫn hướng",
            //            ImagePath = "pack://application:,,,/IntechApplication;component/Resources/Images/05.png",
            //            HeSo = "Hệ số làm việc = 0.7"
            //        }
            //    };

            //    // Gán dữ liệu cho ComboBox
            //    TaskComboBox.ItemsSource = tasks;
        }

        private void CopyText_Click(object sender, RoutedEventArgs e)
        {
            var menuItem = sender as MenuItem;
            var textBlock = (menuItem?.Parent as ContextMenu)?.PlacementTarget as TextBlock;

            if (textBlock != null)
            {
                Clipboard.SetText(textBlock.Text);
            }
        }
        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        //public class TaskItem
        //{
        //    public string Title { get; set; }
        //    public string ImagePath { get; set; }
        //    public string HeSo { get; set; }
        //}
        //private void TaskComboBox_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
        //{
        //    if (TaskComboBox.SelectedItem is TaskItem item)
        //    {
        //        TaskImage.Source = new System.Windows.Media.Imaging.BitmapImage(new System.Uri(item.ImagePath));
        //        HeSoText.Text = item.HeSo;
        //    }
        //}

    }
}
